# API Site - Structure Réorganisée

## Nouvelle Architecture : Package by Feature

L'API des sites a été réorganisée selon une approche "package by feature" où chaque entité métier a son propre package contenant tous ses composants.

## Structure du Package Site

```
src/main/java/com/workeem/workeem_api/tenant/site/
├── Site.java                      # Entité JPA
├── SiteRepository.java            # Repository Spring Data
├── SiteService.java               # Interface du service
├── SiteServiceImpl.java           # Implémentation du service
├── SiteController.java            # Contrôleur REST
├── SiteControllerAdvice.java      # Gestion des exceptions
├── CreateSiteRequestDto.java      # DTO de création
├── UpdateSiteRequestDto.java      # DTO de mise à jour
├── SiteResponseDto.java           # DTO de réponse
├── SiteStatsDto.java              # DTO des statistiques
├── SiteNotFoundException.java     # Exception métier
└── SiteAlreadyExistsException.java # Exception métier
```

## Avantages de cette Structure

### 1. **Cohésion Élevée**
- Tous les composants liés aux sites sont dans le même package
- Facilite la navigation et la compréhension du code
- Réduit les dépendances inter-packages

### 2. **Encapsulation**
- Les détails d'implémentation sont mieux encapsulés
- Les exceptions métier sont spécifiques au domaine
- Le Controller Advice est ciblé sur le package site

### 3. **Maintenabilité**
- Modifications isolées dans un seul package
- Facilite les refactorings
- Réduction des conflits lors du travail en équipe

### 4. **Évolutivité**
- Facile d'ajouter de nouvelles entités (espace, membre, etc.)
- Chaque feature peut évoluer indépendamment
- Structure préparée pour une architecture microservices

## Composants Principaux

### Entité et Repository
- **Site.java** : Entité JPA avec audit automatique via BaseEntity
- **SiteRepository.java** : Repository avec méthodes de recherche personnalisées

### Services
- **SiteService.java** : Interface définissant le contrat métier
- **SiteServiceImpl.java** : Implémentation sans cache (comme demandé)

### Contrôleur et Gestion d'Erreurs
- **SiteController.java** : 14 endpoints REST avec sécurité Keycloak
- **SiteControllerAdvice.java** : Gestion globale des exceptions pour les sites

### DTOs
- **CreateSiteRequestDto.java** : Validation complète pour la création
- **UpdateSiteRequestDto.java** : Champs optionnels pour la mise à jour
- **SiteResponseDto.java** : Réponse complète avec audit
- **SiteStatsDto.java** : Statistiques calculées

### Exceptions Métier
- **SiteNotFoundException.java** : Site non trouvé
- **SiteAlreadyExistsException.java** : Nom de site déjà utilisé

## Endpoints Disponibles

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| POST | `/api/sites` | Créer un site |
| GET | `/api/sites` | Lister tous les sites |
| GET | `/api/sites/{id}` | Obtenir un site |
| PUT | `/api/sites/{id}` | Modifier un site |
| DELETE | `/api/sites/{id}` | Supprimer un site |
| GET | `/api/sites/active` | Sites actifs |
| GET | `/api/sites/inactive` | Sites inactifs |
| GET | `/api/sites/search` | Recherche par nom |
| GET | `/api/sites/city/{city}` | Sites par ville |
| GET | `/api/sites/country/{country}` | Sites par pays |
| PATCH | `/api/sites/{id}/toggle-status` | Basculer le statut |
| GET | `/api/sites/{id}/stats` | Statistiques du site |
| GET | `/api/sites/stats/global` | Statistiques globales |
| GET | `/api/sites/exists` | Vérifier existence |

## Sécurité

- **Authentification** : Keycloak JWT
- **Autorisation** : Rôles ADMIN, MANAGER, USER
- **Multi-tenancy** : Support complet avec context tenant

## Base de Données

- **Table** : `sites` avec audit automatique
- **Migration** : Liquibase dans `V1/schema-definition.xml`
- **Index** : Sur `name` et `city` pour optimiser les recherches

## Modifications Apportées

### ✅ Supprimé
- Annotations de cache Redis (`@Cacheable`, `@CacheEvict`)
- Fichiers de tests unitaires et d'intégration
- Structure en couches (controller/, service/, repository/, dto/)

### ✅ Ajouté
- Package by feature : `com.workeem.workeem_api.tenant.site`
- Controller Advice spécifique au domaine site
- Encapsulation complète dans un seul package

### ✅ Conservé
- Toutes les fonctionnalités métier
- Validation des données
- Gestion des exceptions
- Sécurité Keycloak
- Multi-tenancy
- Audit automatique

## Prochaines Étapes

1. **Tester l'API** avec Postman ou cURL
2. **Intégrer avec le frontend** Angular
3. **Ajouter d'autres entités** (espace, membre) avec la même structure
4. **Configurer la base de données** et exécuter les migrations Liquibase

## Exemple d'Utilisation

```bash
# Créer un site
curl -X POST http://localhost:8080/api/sites \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-Tenant-ID: syllabus" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Workeem Paris",
    "city": "Paris",
    "country": "France",
    "address": "123 Avenue des Champs-Élysées",
    "isActive": true,
    "spacesCount": 25,
    "membersCount": 150
  }'

# Obtenir tous les sites
curl -X GET http://localhost:8080/api/sites \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-Tenant-ID: syllabus"
```

La nouvelle structure est maintenant prête et compilée avec succès ! 🚀
