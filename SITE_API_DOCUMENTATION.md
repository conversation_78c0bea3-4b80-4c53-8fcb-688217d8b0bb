# Site Management API Documentation

## Overview

Cette API permet la gestion des sites dans l'application Workeem. Elle suit les bonnes pratiques Spring Boot avec une architecture multi-tenant, utilise Keycloak pour l'authentification, et implémente un système de cache avec Redis.

## Architecture

- **Multi-tenant**: Chaque tenant a sa propre base de données
- **Authentification**: Keycloak JWT
- **Cache**: Redis avec clés tenant-aware
- **Base de données**: PostgreSQL avec Liquibase pour les migrations
- **Validation**: Bean Validation (JSR-303)
- **Exception Handling**: Controller Advice global

## Endpoints

### Base URL
```
/api/sites
```

### 1. Créer un site
```http
POST /api/sites
```

**Autorisation**: `ADMIN` ou `MANAGER`

**Body**:
```json
{
  "name": "Workeem Paris",
  "city": "Paris",
  "country": "France",
  "address": "123 Avenue des Champs-Élysées, 75008 Paris",
  "imageUrl": "https://example.com/image.jpg",
  "isActive": true,
  "spacesCount": 25,
  "membersCount": 150
}
```

**Response** (201 Created):
```json
{
  "id": 1,
  "name": "Workeem Paris",
  "city": "Paris",
  "country": "France",
  "address": "123 Avenue des Champs-Élysées, 75008 Paris",
  "imageUrl": "https://example.com/image.jpg",
  "isActive": true,
  "spacesCount": 25,
  "membersCount": 150,
  "createdDate": "2024-12-03T10:30:00",
  "lastModifiedDate": null,
  "createdBy": "admin",
  "lastModifiedBy": null
}
```

### 2. Obtenir tous les sites
```http
GET /api/sites
```

**Autorisation**: `ADMIN`, `MANAGER` ou `USER`

**Response** (200 OK):
```json
[
  {
    "id": 1,
    "name": "Workeem Paris",
    "city": "Paris",
    "country": "France",
    "address": "123 Avenue des Champs-Élysées, 75008 Paris",
    "imageUrl": "https://example.com/image.jpg",
    "isActive": true,
    "spacesCount": 25,
    "membersCount": 150,
    "createdDate": "2024-12-03T10:30:00",
    "lastModifiedDate": null,
    "createdBy": "admin",
    "lastModifiedBy": null
  }
]
```

### 3. Obtenir un site par ID
```http
GET /api/sites/{siteId}
```

**Autorisation**: `ADMIN`, `MANAGER` ou `USER`

**Response** (200 OK): Même structure que la création

### 4. Mettre à jour un site
```http
PUT /api/sites/{siteId}
```

**Autorisation**: `ADMIN` ou `MANAGER`

**Body** (tous les champs sont optionnels):
```json
{
  "name": "Workeem Paris Updated",
  "city": "Paris",
  "isActive": false
}
```

### 5. Supprimer un site
```http
DELETE /api/sites/{siteId}
```

**Autorisation**: `ADMIN`

**Response** (204 No Content)

### 6. Obtenir les sites actifs
```http
GET /api/sites/active
```

**Autorisation**: `ADMIN`, `MANAGER` ou `USER`

### 7. Obtenir les sites inactifs
```http
GET /api/sites/inactive
```

**Autorisation**: `ADMIN` ou `MANAGER`

### 8. Rechercher des sites par nom
```http
GET /api/sites/search?name=Paris
```

**Autorisation**: `ADMIN`, `MANAGER` ou `USER`

### 9. Obtenir les sites par ville
```http
GET /api/sites/city/{city}
```

**Autorisation**: `ADMIN`, `MANAGER` ou `USER`

### 10. Obtenir les sites par pays
```http
GET /api/sites/country/{country}
```

**Autorisation**: `ADMIN`, `MANAGER` ou `USER`

### 11. Basculer le statut d'un site
```http
PATCH /api/sites/{siteId}/toggle-status
```

**Autorisation**: `ADMIN` ou `MANAGER`

### 12. Obtenir les statistiques d'un site
```http
GET /api/sites/{siteId}/stats
```

**Autorisation**: `ADMIN` ou `MANAGER`

**Response**:
```json
{
  "totalSpaces": 25,
  "totalMembers": 150,
  "occupancyRate": 85.0,
  "monthlyRevenue": 22500.00
}
```

### 13. Obtenir les statistiques globales
```http
GET /api/sites/stats/global
```

**Autorisation**: `ADMIN` ou `MANAGER`

### 14. Vérifier l'existence d'un site par nom
```http
GET /api/sites/exists?name=Workeem Paris
```

**Autorisation**: `ADMIN` ou `MANAGER`

**Response**:
```json
true
```

## Gestion des erreurs

### Codes d'erreur

- **400 Bad Request**: Données de validation invalides
- **401 Unauthorized**: Non authentifié
- **403 Forbidden**: Pas les permissions nécessaires
- **404 Not Found**: Site non trouvé
- **409 Conflict**: Site avec ce nom existe déjà
- **500 Internal Server Error**: Erreur serveur

### Format des erreurs

```json
{
  "timestamp": "2024-12-03T10:30:00",
  "status": 404,
  "error": "Site Not Found",
  "message": "Site with ID 1 not found",
  "path": "/api/sites"
}
```

### Erreurs de validation

```json
{
  "timestamp": "2024-12-03T10:30:00",
  "status": 400,
  "error": "Validation Failed",
  "message": "Invalid input data",
  "path": "/api/sites",
  "validationErrors": {
    "name": "Site name is required",
    "city": "City is required"
  }
}
```

## Headers requis

- **Authorization**: `Bearer {JWT_TOKEN}`
- **X-Tenant-ID**: `{TENANT_ID}` (pour le multi-tenancy)
- **Content-Type**: `application/json` (pour les requêtes POST/PUT)

## Exemples avec cURL

### Créer un site
```bash
curl -X POST http://localhost:8080/api/sites \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-Tenant-ID: syllabus" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Workeem Test",
    "city": "Test City",
    "country": "Test Country",
    "address": "123 Test Street",
    "isActive": true,
    "spacesCount": 10,
    "membersCount": 50
  }'
```

### Obtenir tous les sites
```bash
curl -X GET http://localhost:8080/api/sites \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "X-Tenant-ID: syllabus"
```

## Cache

L'API utilise Redis pour le cache avec les clés suivantes :
- `{tenantId}:sites` - Liste de tous les sites
- `{tenantId}:site:{siteId}` - Site individuel
- `{tenantId}:activeSites` - Sites actifs
- `{tenantId}:globalStats` - Statistiques globales

Le cache est automatiquement invalidé lors des opérations de création, modification et suppression.
