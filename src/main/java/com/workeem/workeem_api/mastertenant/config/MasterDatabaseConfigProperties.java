package com.workeem.workeem_api.mastertenant.config;

import lombok.Getter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


@Getter
@ToString
@Configuration
@ConfigurationProperties("multitenancy.master.datasource")
public class MasterDatabaseConfigProperties {

    private String url;
    private String username;
    private String password;
    private String driverClassName;
    private long connectionTimeout;
    private int maxPoolSize;
    private long idleTimeout;
    private int minIdle;
    private String poolName;

    public MasterDatabaseConfigProperties setUrl(String url) {
        this.url = url;
        return this;
    }

    public MasterDatabaseConfigProperties setUsername(String username) {
        this.username = username;
        return this;
    }

    public MasterDatabaseConfigProperties setPassword(String password) {
        this.password = password;
        return this;
    }

    public MasterDatabaseConfigProperties setDriverClassName(String driverClassName) {
        this.driverClassName = driverClassName;
        return this;
    }

    public MasterDatabaseConfigProperties setConnectionTimeout(long connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
        return this;
    }

    public MasterDatabaseConfigProperties setMaxPoolSize(int maxPoolSize) {
        this.maxPoolSize = maxPoolSize;
        return this;
    }

    public MasterDatabaseConfigProperties setIdleTimeout(long idleTimeout) {
        this.idleTimeout = idleTimeout;
        return this;
    }

    public MasterDatabaseConfigProperties setMinIdle(int minIdle) {
        this.minIdle = minIdle;
        return this;
    }

    public MasterDatabaseConfigProperties setPoolName(String poolName) {
        this.poolName = poolName;
        return this;
    }
}
