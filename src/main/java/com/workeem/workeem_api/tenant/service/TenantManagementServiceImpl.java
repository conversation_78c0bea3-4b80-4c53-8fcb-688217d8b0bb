package com.workeem.workeem_api.tenant.service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import javax.sql.DataSource;

import com.workeem.workeem_api.mastertenant.entity.Tenant;
import com.workeem.workeem_api.mastertenant.repository.MasterTenantRepository;
import com.workeem.workeem_api.util.EncryptionService;
import liquibase.exception.LiquibaseException;
import liquibase.integration.spring.SpringLiquibase;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;
import org.springframework.stereotype.Service;

@Service
@EnableConfigurationProperties(LiquibaseProperties.class)
public class TenantManagementServiceImpl implements TenantManagementService {

    private static final String VALID_DATABASE_NAME_REGEXP = "[A-Za-z0-9_]*";

    private final EncryptionService encryptionService;
    private final LiquibaseProperties liquibaseProperties;
    private final ResourceLoader resourceLoader;
    private final MasterTenantRepository tenantRepository;

    public TenantManagementServiceImpl(EncryptionService encryptionService,
                                       @Qualifier("tenantLiquibaseProperties") LiquibaseProperties liquibaseProperties,
                                       ResourceLoader resourceLoader, MasterTenantRepository tenantRepository) {
        this.encryptionService = encryptionService;
        this.liquibaseProperties = liquibaseProperties;
        this.resourceLoader = resourceLoader;
        this.tenantRepository = tenantRepository;
    }

    @Value("${multitenancy.tenant.datasource.url-prefix}")
    private String defaultUrlPrefix;

    @Value("${multitenancy.tenant.datasource.driver-class}")
    private String defaultDriverClass;

    @Value("${encryption.secret}")
    private String secret;

    @Value("${encryption.salt}")
    private String salt;

    @Override
    public void createTenant(String tenantId, String dbName, String urlPrefix, String username, String password, String driverClass) {
        // Verify db string to prevent SQL injection
        if (!dbName.matches(VALID_DATABASE_NAME_REGEXP)) {
            throw new TenantCreationException("Invalid db name: " + dbName);
        }

        String url = getDbUrl(dbName, urlPrefix);
        String driver = getDbDriverClass(driverClass);

        String encryptedUsername = encryptionService.encrypt(username, secret, salt);
        String encryptedPassword = encryptionService.encrypt(password, secret, salt);

        try (Connection connection = DriverManager.getConnection(url, username, password)) {
            DataSource tenantDataSource = new SingleConnectionDataSource(connection, false);
            runLiquibase(tenantDataSource);
        } catch (SQLException | LiquibaseException e) {
            throw new TenantCreationException("Error when populating db: ", e);
        }
        Tenant tenant = Tenant.builder()
                .tenantId(tenantId)
                .dbName(dbName)
                .url(url)
                .username(encryptedUsername)
                .password(encryptedPassword)
                .driverClass(driver)
                .build();
        tenantRepository.save(tenant);
    }

    private String getDbUrl(String dbName, String urlPrefix) {
        String url;
        if (urlPrefix == null) {
            url = defaultUrlPrefix + dbName;
        } else {
            url = urlPrefix + dbName;
        }
        return url;
    }

    private String getDbDriverClass(String driverClass) {
        String driver;
        if (driverClass == null) {
            driver = defaultDriverClass;
        } else {
            driver = driverClass;
        }
        return driver;
    }

    private void runLiquibase(DataSource dataSource) throws LiquibaseException {
        SpringLiquibase liquibase = getSpringLiquibase(dataSource);
        liquibase.afterPropertiesSet();
    }

    protected SpringLiquibase getSpringLiquibase(DataSource dataSource) {
        SpringLiquibase liquibase = new SpringLiquibase();
        liquibase.setResourceLoader(resourceLoader);
        liquibase.setDataSource(dataSource);
        liquibase.setChangeLog(liquibaseProperties.getChangeLog());
        liquibase.setContexts(liquibaseProperties.getContexts());
        liquibase.setDefaultSchema(liquibaseProperties.getDefaultSchema());
        liquibase.setLiquibaseSchema(liquibaseProperties.getLiquibaseSchema());
        liquibase.setLiquibaseTablespace(liquibaseProperties.getLiquibaseTablespace());
        liquibase.setDatabaseChangeLogTable(liquibaseProperties.getDatabaseChangeLogTable());
        liquibase.setDatabaseChangeLogLockTable(liquibaseProperties.getDatabaseChangeLogLockTable());
        liquibase.setDropFirst(liquibaseProperties.isDropFirst());
        liquibase.setShouldRun(liquibaseProperties.isEnabled());
        liquibase.setLabelFilter(liquibaseProperties.getLabelFilter());
        liquibase.setChangeLogParameters(liquibaseProperties.getParameters());
        liquibase.setRollbackFile(liquibaseProperties.getRollbackFile());
        liquibase.setTestRollbackOnUpdate(liquibaseProperties.isTestRollbackOnUpdate());
        return liquibase;
    }
}
