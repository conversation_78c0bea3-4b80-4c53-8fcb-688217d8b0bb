package com.workeem.workeem_api.tenant.service;

import com.workeem.workeem_api.mastertenant.config.TenantContext;
import com.workeem.workeem_api.tenant.entity.Product;
import com.workeem.workeem_api.tenant.repository.ProductRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@RequiredArgsConstructor
public class ProductServiceImpl implements ProductService {

    private final ProductRepository productRepository;

    //@Cacheable(value = "products", keyGenerator = "customKeyGenerator")
    @Override
    public List<Product> getAllProduct() {
        return productRepository.findAll();
    }

    @Cacheable(value = "product", keyGenerator = "customKeyGenerator")
    @Override
    public Product getProductById(Integer id) {
        return productRepository.findById(id).orElseThrow(
                () -> new EntityNotFoundException("Product by ID " + id + " not found")
        );
    }

    @Override
    public void updateProduct(Integer productId, Product product) {
        Product productToUpdate = productRepository.findById(productId).orElseThrow(
                () -> new EntityNotFoundException("Product by ID " + productId + " not found")
        );

        productToUpdate.setProductId(productId);
        productToUpdate.setProductName(product.getProductName());
        productToUpdate.setQuantity(product.getQuantity());
        productToUpdate.setSize(product.getSize());

        productRepository.save(productToUpdate);
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = "products", keyGenerator = "customKeyGenerator", allEntries = true)
    })
    @Override
    public void createProduct(Product product) {
        String productName = product.getProductName();
        product.setProductName(TenantContext.getCurrentTenant() + "::" + productName);
        productRepository.save(product);
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = "product", keyGenerator = "customKeyGenerator"),
            @CacheEvict(cacheNames = "products", keyGenerator = "customKeyGenerator", allEntries = true)
    })
    @Override
    public void deleteProductById(Integer id) {
        if (!productRepository.existsById(id)) {
            throw new EntityNotFoundException("Product by ID " + id + " not found");
        }
        productRepository.deleteById(id);
    }
}
