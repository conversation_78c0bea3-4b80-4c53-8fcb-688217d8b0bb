package com.workeem.workeem_api.tenant.site;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SiteServiceImpl implements SiteService {

    private final SiteRepository siteRepository;

    @Override
    public SiteResponseDto createSite(CreateSiteRequestDto createSiteRequestDto) {
        log.info("Creating new site with name: {}", createSiteRequestDto.getName());

        // Check if site with same name already exists
        if (siteRepository.existsByNameIgnoreCase(createSiteRequestDto.getName())) {
            throw new SiteAlreadyExistsException("Site with name '" + createSiteRequestDto.getName() + "' already exists");
        }

        Site site = Site.builder()
                .name(createSiteRequestDto.getName())
                .city(createSiteRequestDto.getCity())
                .country(createSiteRequestDto.getCountry())
                .address(createSiteRequestDto.getAddress())
                .imageUrl(createSiteRequestDto.getImageUrl())
                .isActive(createSiteRequestDto.getIsActive())
                .spacesCount(createSiteRequestDto.getSpacesCount())
                .membersCount(createSiteRequestDto.getMembersCount())
                .build();

        Site savedSite = siteRepository.save(site);
        log.info("Site created successfully with ID: {}", savedSite.getId());

        return mapToResponseDto(savedSite);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SiteResponseDto> getAllSites() {
        log.debug("Fetching all sites");
        return siteRepository.findAllByOrderByCreatedDateDesc()
                .stream()
                .map(this::mapToResponseDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public SiteResponseDto getSiteById(Long siteId) {
        log.debug("Fetching site with ID: {}", siteId);
        Site site = siteRepository.findById(siteId)
                .orElseThrow(() -> new SiteNotFoundException(siteId));
        return mapToResponseDto(site);
    }

    @Override
    public SiteResponseDto updateSite(Long siteId, UpdateSiteRequestDto updateSiteRequestDto) {
        log.info("Updating site with ID: {}", siteId);

        Site existingSite = siteRepository.findById(siteId)
                .orElseThrow(() -> new SiteNotFoundException(siteId));

        // Check if name is being changed and if new name already exists
        if (updateSiteRequestDto.getName() != null && 
            !updateSiteRequestDto.getName().equalsIgnoreCase(existingSite.getName()) &&
            siteRepository.existsByNameIgnoreCase(updateSiteRequestDto.getName())) {
            throw new SiteAlreadyExistsException("Site with name '" + updateSiteRequestDto.getName() + "' already exists");
        }

        // Update fields if provided
        if (updateSiteRequestDto.getName() != null) {
            existingSite.setName(updateSiteRequestDto.getName());
        }
        if (updateSiteRequestDto.getCity() != null) {
            existingSite.setCity(updateSiteRequestDto.getCity());
        }
        if (updateSiteRequestDto.getCountry() != null) {
            existingSite.setCountry(updateSiteRequestDto.getCountry());
        }
        if (updateSiteRequestDto.getAddress() != null) {
            existingSite.setAddress(updateSiteRequestDto.getAddress());
        }
        if (updateSiteRequestDto.getImageUrl() != null) {
            existingSite.setImageUrl(updateSiteRequestDto.getImageUrl());
        }
        if (updateSiteRequestDto.getIsActive() != null) {
            existingSite.setIsActive(updateSiteRequestDto.getIsActive());
        }
        if (updateSiteRequestDto.getSpacesCount() != null) {
            existingSite.setSpacesCount(updateSiteRequestDto.getSpacesCount());
        }
        if (updateSiteRequestDto.getMembersCount() != null) {
            existingSite.setMembersCount(updateSiteRequestDto.getMembersCount());
        }

        Site updatedSite = siteRepository.save(existingSite);
        log.info("Site updated successfully with ID: {}", updatedSite.getId());

        return mapToResponseDto(updatedSite);
    }

    @Override
    public void deleteSite(Long siteId) {
        log.info("Deleting site with ID: {}", siteId);

        if (!siteRepository.existsById(siteId)) {
            throw new SiteNotFoundException(siteId);
        }

        siteRepository.deleteById(siteId);
        log.info("Site deleted successfully with ID: {}", siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SiteResponseDto> getActiveSites() {
        log.debug("Fetching all active sites");
        return siteRepository.findByIsActiveTrue()
                .stream()
                .map(this::mapToResponseDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SiteResponseDto> getInactiveSites() {
        log.debug("Fetching all inactive sites");
        return siteRepository.findByIsActiveFalse()
                .stream()
                .map(this::mapToResponseDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SiteResponseDto> searchSitesByName(String name) {
        log.debug("Searching sites by name: {}", name);
        return siteRepository.findByNameContainingIgnoreCase(name)
                .stream()
                .map(this::mapToResponseDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SiteResponseDto> getSitesByCity(String city) {
        log.debug("Fetching sites by city: {}", city);
        return siteRepository.findByCityIgnoreCase(city)
                .stream()
                .map(this::mapToResponseDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SiteResponseDto> getSitesByCountry(String country) {
        log.debug("Fetching sites by country: {}", country);
        return siteRepository.findByCountryIgnoreCase(country)
                .stream()
                .map(this::mapToResponseDto)
                .collect(Collectors.toList());
    }

    @Override
    public SiteResponseDto toggleSiteStatus(Long siteId) {
        log.info("Toggling status for site with ID: {}", siteId);

        Site site = siteRepository.findById(siteId)
                .orElseThrow(() -> new SiteNotFoundException(siteId));

        site.setIsActive(!site.getIsActive());
        Site updatedSite = siteRepository.save(site);

        log.info("Site status toggled successfully. Site ID: {}, New status: {}", 
                 siteId, updatedSite.getIsActive());

        return mapToResponseDto(updatedSite);
    }

    @Override
    @Transactional(readOnly = true)
    public SiteStatsDto getSiteStats(Long siteId) {
        log.debug("Fetching stats for site with ID: {}", siteId);

        Site site = siteRepository.findById(siteId)
                .orElseThrow(() -> new SiteNotFoundException(siteId));

        // For now, return basic stats from the site entity
        // In the future, this could be enhanced with real-time calculations
        return SiteStatsDto.builder()
                .totalSpaces(site.getSpacesCount())
                .totalMembers(site.getMembersCount())
                .occupancyRate(calculateOccupancyRate(site))
                .monthlyRevenue(calculateMonthlyRevenue(site))
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public SiteStatsDto getGlobalStats() {
        log.debug("Fetching global stats for all sites");

        Integer totalSpaces = siteRepository.getTotalSpacesCount();
        Integer totalMembers = siteRepository.getTotalMembersCount();

        return SiteStatsDto.builder()
                .totalSpaces(totalSpaces != null ? totalSpaces : 0)
                .totalMembers(totalMembers != null ? totalMembers : 0)
                .occupancyRate(calculateGlobalOccupancyRate())
                .monthlyRevenue(calculateGlobalMonthlyRevenue())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name) {
        return siteRepository.existsByNameIgnoreCase(name);
    }

    // Private helper methods
    private SiteResponseDto mapToResponseDto(Site site) {
        return SiteResponseDto.builder()
                .id(site.getId())
                .name(site.getName())
                .city(site.getCity())
                .country(site.getCountry())
                .address(site.getAddress())
                .imageUrl(site.getImageUrl())
                .isActive(site.getIsActive())
                .spacesCount(site.getSpacesCount())
                .membersCount(site.getMembersCount())
                .createdDate(site.getCreatedDate())
                .lastModifiedDate(site.getLastModifiedDate())
                .createdBy(site.getCreatedBy())
                .lastModifiedBy(site.getLastModifiedBy())
                .build();
    }

    private Double calculateOccupancyRate(Site site) {
        // Mock calculation - in real implementation, this would calculate based on actual bookings
        if (site.getSpacesCount() == 0) {
            return 0.0;
        }
        return Math.min(85.0 + (site.getMembersCount() * 0.1), 100.0);
    }

    private BigDecimal calculateMonthlyRevenue(Site site) {
        // Mock calculation - in real implementation, this would be based on actual revenue data
        return BigDecimal.valueOf(site.getMembersCount() * 150.0);
    }

    private Double calculateGlobalOccupancyRate() {
        // Mock calculation for global occupancy rate
        return 78.5;
    }

    private BigDecimal calculateGlobalMonthlyRevenue() {
        // Mock calculation for global monthly revenue
        Integer totalMembers = siteRepository.getTotalMembersCount();
        return BigDecimal.valueOf((totalMembers != null ? totalMembers : 0) * 150.0);
    }
}
