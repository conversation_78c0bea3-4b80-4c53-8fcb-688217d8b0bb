package com.workeem.workeem_api.tenant.site;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/sites")
@RequiredArgsConstructor
@Slf4j
public class SiteController {

    private final SiteService siteService;

    /**
     * Create a new site
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<SiteResponseDto> createSite(@Valid @RequestBody CreateSiteRequestDto createSiteRequestDto) {
        log.info("REST request to create site: {}", createSiteRequestDto.getName());
        SiteResponseDto createdSite = siteService.createSite(createSiteRequestDto);
        return new ResponseEntity<>(createdSite, HttpStatus.CREATED);
    }

    /**
     * Get all sites
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('USER')")
    public ResponseEntity<List<SiteResponseDto>> getAllSites() {
        log.debug("REST request to get all sites");
        List<SiteResponseDto> sites = siteService.getAllSites();
        return ResponseEntity.ok(sites);
    }

    /**
     * Get site by ID
     */
    @GetMapping("/{siteId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('USER')")
    public ResponseEntity<SiteResponseDto> getSiteById(@PathVariable Long siteId) {
        log.debug("REST request to get site by ID: {}", siteId);
        SiteResponseDto site = siteService.getSiteById(siteId);
        return ResponseEntity.ok(site);
    }

    /**
     * Update site
     */
    @PutMapping("/{siteId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<SiteResponseDto> updateSite(
            @PathVariable Long siteId,
            @Valid @RequestBody UpdateSiteRequestDto updateSiteRequestDto) {
        log.info("REST request to update site with ID: {}", siteId);
        SiteResponseDto updatedSite = siteService.updateSite(siteId, updateSiteRequestDto);
        return ResponseEntity.ok(updatedSite);
    }

    /**
     * Delete site
     */
    @DeleteMapping("/{siteId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteSite(@PathVariable Long siteId) {
        log.info("REST request to delete site with ID: {}", siteId);
        siteService.deleteSite(siteId);
        return ResponseEntity.noContent().build();
    }

    /**
     * Get all active sites
     */
    @GetMapping("/active")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('USER')")
    public ResponseEntity<List<SiteResponseDto>> getActiveSites() {
        log.debug("REST request to get all active sites");
        List<SiteResponseDto> activeSites = siteService.getActiveSites();
        return ResponseEntity.ok(activeSites);
    }

    /**
     * Get all inactive sites
     */
    @GetMapping("/inactive")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<List<SiteResponseDto>> getInactiveSites() {
        log.debug("REST request to get all inactive sites");
        List<SiteResponseDto> inactiveSites = siteService.getInactiveSites();
        return ResponseEntity.ok(inactiveSites);
    }

    /**
     * Search sites by name
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('USER')")
    public ResponseEntity<List<SiteResponseDto>> searchSitesByName(@RequestParam String name) {
        log.debug("REST request to search sites by name: {}", name);
        List<SiteResponseDto> sites = siteService.searchSitesByName(name);
        return ResponseEntity.ok(sites);
    }

    /**
     * Get sites by city
     */
    @GetMapping("/city/{city}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('USER')")
    public ResponseEntity<List<SiteResponseDto>> getSitesByCity(@PathVariable String city) {
        log.debug("REST request to get sites by city: {}", city);
        List<SiteResponseDto> sites = siteService.getSitesByCity(city);
        return ResponseEntity.ok(sites);
    }

    /**
     * Get sites by country
     */
    @GetMapping("/country/{country}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('USER')")
    public ResponseEntity<List<SiteResponseDto>> getSitesByCountry(@PathVariable String country) {
        log.debug("REST request to get sites by country: {}", country);
        List<SiteResponseDto> sites = siteService.getSitesByCountry(country);
        return ResponseEntity.ok(sites);
    }

    /**
     * Toggle site status (active/inactive)
     */
    @PatchMapping("/{siteId}/toggle-status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<SiteResponseDto> toggleSiteStatus(@PathVariable Long siteId) {
        log.info("REST request to toggle status for site with ID: {}", siteId);
        SiteResponseDto updatedSite = siteService.toggleSiteStatus(siteId);
        return ResponseEntity.ok(updatedSite);
    }

    /**
     * Get site statistics
     */
    @GetMapping("/{siteId}/stats")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<SiteStatsDto> getSiteStats(@PathVariable Long siteId) {
        log.debug("REST request to get stats for site with ID: {}", siteId);
        SiteStatsDto stats = siteService.getSiteStats(siteId);
        return ResponseEntity.ok(stats);
    }

    /**
     * Get global statistics for all sites
     */
    @GetMapping("/stats/global")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<SiteStatsDto> getGlobalStats() {
        log.debug("REST request to get global stats");
        SiteStatsDto globalStats = siteService.getGlobalStats();
        return ResponseEntity.ok(globalStats);
    }

    /**
     * Check if site exists by name
     */
    @GetMapping("/exists")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<Boolean> existsByName(@RequestParam String name) {
        log.debug("REST request to check if site exists by name: {}", name);
        boolean exists = siteService.existsByName(name);
        return ResponseEntity.ok(exists);
    }
}
