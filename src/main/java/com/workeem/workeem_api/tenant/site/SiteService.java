package com.workeem.workeem_api.tenant.site;

import java.util.List;

public interface SiteService {

    /**
     * Create a new site
     */
    SiteResponseDto createSite(CreateSiteRequestDto createSiteRequestDto);

    /**
     * Get all sites
     */
    List<SiteResponseDto> getAllSites();

    /**
     * Get site by ID
     */
    SiteResponseDto getSiteById(Long siteId);

    /**
     * Update site
     */
    SiteResponseDto updateSite(Long siteId, UpdateSiteRequestDto updateSiteRequestDto);

    /**
     * Delete site by ID
     */
    void deleteSite(Long siteId);

    /**
     * Get all active sites
     */
    List<SiteResponseDto> getActiveSites();

    /**
     * Get all inactive sites
     */
    List<SiteResponseDto> getInactiveSites();

    /**
     * Search sites by name
     */
    List<SiteResponseDto> searchSitesByName(String name);

    /**
     * Get sites by city
     */
    List<SiteResponseDto> getSitesByCity(String city);

    /**
     * Get sites by country
     */
    List<SiteResponseDto> getSitesByCountry(String country);

    /**
     * Toggle site status (active/inactive)
     */
    SiteResponseDto toggleSiteStatus(Long siteId);

    /**
     * Get site statistics
     */
    SiteStatsDto getSiteStats(Long siteId);

    /**
     * Get global statistics for all sites
     */
    SiteStatsDto getGlobalStats();

    /**
     * Check if site exists by name
     */
    boolean existsByName(String name);
}
