package com.workeem.workeem_api.tenant.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SiteResponseDto implements Serializable {

    private Long id;
    private String name;
    private String city;
    private String country;
    private String address;
    private String imageUrl;
    private Boolean isActive;
    private Integer spacesCount;
    private Integer membersCount;
    private LocalDateTime createdDate;
    private LocalDateTime lastModifiedDate;
    private String createdBy;
    private String lastModifiedBy;
}
