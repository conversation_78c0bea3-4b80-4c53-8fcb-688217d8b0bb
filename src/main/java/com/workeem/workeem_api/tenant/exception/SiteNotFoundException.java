package com.workeem.workeem_api.tenant.exception;

public class SiteNotFoundException extends RuntimeException {
    
    public SiteNotFoundException(String message) {
        super(message);
    }
    
    public SiteNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public SiteNotFoundException(Long siteId) {
        super("Site with ID " + siteId + " not found");
    }
}
