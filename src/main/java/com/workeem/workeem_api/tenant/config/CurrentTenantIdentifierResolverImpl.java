package com.workeem.workeem_api.tenant.config;

import com.nimbusds.oauth2.sdk.util.StringUtils;
import com.workeem.workeem_api.mastertenant.config.TenantContext;
import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.springframework.beans.factory.annotation.Value;


public class CurrentTenantIdentifierResolverImpl implements CurrentTenantIdentifierResolver {

    @Value("${multitenancy.http.defaultTenantId}")
    private String defaultTenantId;

    @Override
    public String resolveCurrentTenantIdentifier() {
        String tenant = TenantContext.getCurrentTenant();
        return StringUtils.isNotBlank(tenant) ? tenant : defaultTenantId;
    }

    @Override
    public boolean validateExistingCurrentSessions() {
        return true;
    }
}
