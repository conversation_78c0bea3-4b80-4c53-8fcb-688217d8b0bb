package com.workeem.workeem_api.tenant.repository;

import com.workeem.workeem_api.tenant.entity.Site;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SiteRepository extends JpaRepository<Site, Long> {

    /**
     * Find site by name (case-insensitive)
     */
    Optional<Site> findByNameIgnoreCase(String name);

    /**
     * Check if site exists by name (case-insensitive)
     */
    boolean existsByNameIgnoreCase(String name);

    /**
     * Find all active sites
     */
    List<Site> findByIsActiveTrue();

    /**
     * Find all inactive sites
     */
    List<Site> findByIsActiveFalse();

    /**
     * Find sites by city (case-insensitive)
     */
    List<Site> findByCityIgnoreCase(String city);

    /**
     * Find sites by country (case-insensitive)
     */
    List<Site> findByCountryIgnoreCase(String country);

    /**
     * Search sites by name containing text (case-insensitive)
     */
    List<Site> findByNameContainingIgnoreCase(String name);

    /**
     * Get total count of active sites
     */
    @Query("SELECT COUNT(s) FROM Site s WHERE s.isActive = true")
    long countActiveSites();

    /**
     * Get total spaces count across all sites
     */
    @Query("SELECT COALESCE(SUM(s.spacesCount), 0) FROM Site s WHERE s.isActive = true")
    Integer getTotalSpacesCount();

    /**
     * Get total members count across all sites
     */
    @Query("SELECT COALESCE(SUM(s.membersCount), 0) FROM Site s WHERE s.isActive = true")
    Integer getTotalMembersCount();

    /**
     * Find sites ordered by creation date (newest first)
     */
    List<Site> findAllByOrderByCreatedDateDesc();

    /**
     * Find sites by status ordered by name
     */
    List<Site> findByIsActiveOrderByNameAsc(Boolean isActive);
}
