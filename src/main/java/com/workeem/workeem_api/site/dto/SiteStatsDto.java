package com.workeem.workeem_api.site.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SiteStatsDto implements Serializable {

    private Integer totalSpaces;
    private Integer totalMembers;
    private Double occupancyRate;
    private BigDecimal monthlyRevenue;
}
