package com.workeem.workeem_api.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Map;

@Component
public class TenantVerificationFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // Retrieve authentication from SecurityContext
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !(authentication.getPrincipal() instanceof Jwt)) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Authentication is not valid");
            return;
        }

        Jwt jwt = (Jwt) authentication.getPrincipal();

        // Retrieve the 'organization' claim from the JWT
        Map<String, Object> organization = jwt.getClaim("organization");
        if (organization == null || organization.isEmpty()) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Organization claim is missing or invalid");
            return;
        }

        // Retrieve X-Tenant-ID value from header
        String tenantFromRequest = request.getHeader("X-Tenant-ID");
        if (tenantFromRequest == null || tenantFromRequest.isBlank()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "X-Tenant-ID header is missing");
            return;
        }

        // Check if the 'organization' claim contains the key corresponding to X-Tenant-ID
        if (!organization.containsKey(tenantFromRequest)) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Tenant mismatch");
            return;
        }

        // If all valid, continue with next filter
        filterChain.doFilter(request, response);
    }
}
