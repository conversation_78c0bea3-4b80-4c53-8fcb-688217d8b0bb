package com.workeem.workeem_api.controller;

import com.workeem.workeem_api.tenant.service.TenantManagementService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@RequestMapping("/")
@RequiredArgsConstructor
public class TenantApiController {

    private final TenantManagementService tenantManagementService;

    @PostMapping("/tenants")
    public ResponseEntity<Void> createTenant(
            @RequestParam String tenantId,
            @RequestParam String dbName,
            @RequestParam(required = false) String urlPrefix,
            @RequestParam String username,
            @RequestParam String password,
            @RequestParam(required = false) String driverClass) {
        tenantManagementService.createTenant(tenantId, dbName, urlPrefix, username, password, driverClass);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
