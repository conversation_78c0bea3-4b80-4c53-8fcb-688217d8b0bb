package com.workeem.workeem_api.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties("multitenancy.http")
public class MultitenancyConfigProperties {
    private String headerName;
    private String defaultTenantId;
}
