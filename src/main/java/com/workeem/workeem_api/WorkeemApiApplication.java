package com.workeem.workeem_api;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

@SpringBootApplication
@EnableJpaAuditing(auditorAwareRef = "auditorAware")
@EnableCaching
public class WorkeemApiApplication {

	public static void main(String[] args) {
		SpringApplication.run(WorkeemApiApplication.class, args);
	}
}
