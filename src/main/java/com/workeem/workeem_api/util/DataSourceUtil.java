package com.workeem.workeem_api.util;

import com.workeem.workeem_api.mastertenant.entity.Tenant;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.io.Serializable;

@Slf4j
@RequiredArgsConstructor
@Service
public final class DataSourceUtil implements Serializable {

    private final EncryptionService encryptionService;

    @Value("${encryption.secret}")
    private String secret;

    @Value("${encryption.salt}")
    private String salt;

    public DataSource createAndConfigureDataSource(Tenant tenant) {
        HikariDataSource ds = new HikariDataSource();
        ds.setUsername(getDecrypt(tenant.getUsername()));
        ds.setPassword(getDecrypt(tenant.getPassword()));
        ds.setJdbcUrl(tenant.getUrl());
        ds.setDriverClassName(tenant.getDriverClass());
        // HikariCP settings - could come from the master_tenant table but
        // hardcoded here for brevity
        // Maximum waiting time for a connection from the pool
        ds.setConnectionTimeout(20000);
        // Minimum number of idle connections in the pool
        ds.setMinimumIdle(3);
        // Maximum number of actual connection in the pool
        ds.setMaximumPoolSize(500);
        // Maximum time that a connection is allowed to sit idle in the pool
        ds.setIdleTimeout(300000);
        ds.setConnectionTimeout(20000);
        // Setting up a pool name for each tenant datasource
        String tenantConnectionPoolName = tenant.getDbName() + "-connection-pool";
        ds.setPoolName(tenantConnectionPoolName);
        log.info("Configured datasource:{}. Connection pool name:{}", tenant.getDbName(), tenantConnectionPoolName);
        return ds;
    }

    private String getDecrypt(@Size(max = 50) String encryptedValue) {
        return encryptionService.decrypt(encryptedValue, secret, salt);
    }
}
