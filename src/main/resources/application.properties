## Application
spring.application.name=workeem-api
server.port=8080

## Profile
spring.profiles.active=dev
spring.profiles.group.dev=dev,api-docs

## OAuth2 Configuration
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:9090/realms/workeem
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs

## keycloak jwt converter configuration
jwt.auth.converter.resource-id=workeem-api
jwt.auth.converter.principal-attribute=principal_username

## JPA Configuration
spring.jpa.database=postgresql
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

## Multi tenancy properties
multitenancy.http.headerName=X-Tenant-ID
multitenancy.http.defaultTenantId=X-Tenant-ID

## Multitenancy Master datasource configuration + Liquibase
multitenancy.master.datasource.url=******************************************
multitenancy.master.liquibase.changeLog=classpath:db/changelog/db.changelog-master.xml
multitenancy.master.datasource.username=${DB_USERNAME:postgres}
multitenancy.master.datasource.password=${DB_PASSWORD:postgres}
multitenancy.master.datasource.driverClassName=org.postgresql.Driver
multitenancy.master.datasource.connectionTimeout=20000
multitenancy.master.datasource.maxPoolSize=250
multitenancy.master.datasource.idleTimeout=300000
multitenancy.master.datasource.minIdle=5
multitenancy.master.datasource.poolName=masterdb-connection-pool

## Multitenancy Tenant datasource configuration + Liquibase
multitenancy.tenant.liquibase.changeLog=classpath:db/changelog/db.changelog-tenant.xml
multitenancy.tenant.datasource.url-prefix=********************************/
multitenancy.tenant.datasource.driver-class=org.postgresql.Driver

## Vault config
spring.config.import=vault://
spring.cloud.vault.uri=${VAULT_ADDR:http://127.0.0.1:8200}
spring.cloud.vault.token=${VAULT_TOKEN:hvs.WV6MkD1IO1fK3gFNtyiw4nZ3}
spring.cloud.vault.scheme=http
spring.cloud.vault.kv.enabled=true

## AES encryption
encryption.secret=${ENCRYPTION_SECRET:defaultSecret}
encryption.salt=${ENCRYPTION_SALT:defaultSalt}

## Caching configuration
## spring.cache.type=redis
spring.cache.type=none
spring.cache.redis.cache-null-values=true
spring.data.redis.host=localhost
spring.data.redis.port=6379

## Monitoring configuration - Prometheus & Grafana
management.endpoints.web.exposure.include=prometheus