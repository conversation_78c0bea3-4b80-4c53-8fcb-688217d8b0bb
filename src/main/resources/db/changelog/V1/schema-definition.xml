<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Sites table -->
    <changeSet id="create-sites-table" author="workeem">
        <createTable tableName="sites">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="city" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="country" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="image_url" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="spaces_count" type="INTEGER" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="members_count" type="INTEGER" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>

    <!-- Index on site name for faster searches -->
    <changeSet id="create-sites-name-index" author="workeem">
        <createIndex tableName="sites" indexName="idx_sites_name">
            <column name="name"/>
        </createIndex>
    </changeSet>

    <!-- Index on city for location-based queries -->
    <changeSet id="create-sites-city-index" author="workeem">
        <createIndex tableName="sites" indexName="idx_sites_city">
            <column name="city"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>