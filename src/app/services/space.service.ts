import { Injectable } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { delay, map } from 'rxjs/operators';
import {
  Space,
  SpaceType,
  SpaceStatus,
  Equipment,
  EquipmentType,
  EquipmentStatus,
  CreateSpaceRequest,
  UpdateSpaceRequest,
  SpaceSearchFilters,
  SpaceReservation,
  ReservationStatus,
  CalendarEvent,
  CalendarResource
} from '../models/space.model';

@Injectable({
  providedIn: 'root'
})
export class SpaceService {
  private spacesSubject = new BehaviorSubject<Space[]>([]);
  public spaces$ = this.spacesSubject.asObservable();

  private reservationsSubject = new BehaviorSubject<SpaceReservation[]>([]);
  public reservations$ = this.reservationsSubject.asObservable();

  constructor() {
    this.initializeMockData();
  }

  private initializeMockData() {
    const mockSpaces: Space[] = [
      {
        id: '1',
        name: 'Poste de travail A1',
        description: 'Poste de travail individuel avec vue sur jardin',
        type: SpaceType.WORKSTATION,
        capacity: 1,
        location: 'Zone A',
        floor: 'RDC',
        area: 4,
        status: SpaceStatus.AVAILABLE,
        equipment: [
          {
            id: 'eq1',
            name: 'Bureau ajustable',
            type: EquipmentType.DESK,
            quantity: 1,
            status: EquipmentStatus.WORKING
          },
          {
            id: 'eq2',
            name: 'Chaise ergonomique',
            type: EquipmentType.CHAIR,
            quantity: 1,
            status: EquipmentStatus.WORKING
          },
          {
            id: 'eq3',
            name: 'Écran 24"',
            type: EquipmentType.MONITOR,
            brand: 'Dell',
            model: 'U2419H',
            quantity: 1,
            status: EquipmentStatus.WORKING
          }
        ],
        amenities: ['WiFi', 'Prise électrique', 'Éclairage LED'],
        images: ['/assets/spaces/workstation-1.jpg'],
        availability: {
          isActive: true,
          schedule: {
            monday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
            tuesday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
            wednesday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
            thursday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
            friday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
            saturday: { isOpen: false, openTime: '09:00', closeTime: '17:00', breaks: [] },
            sunday: { isOpen: false, openTime: '09:00', closeTime: '17:00', breaks: [] }
          },
          exceptions: [],
          advanceBookingDays: 30,
          minBookingDuration: 60,
          maxBookingDuration: 480,
          bufferTime: 15
        },
        pricing: {
          hourlyRate: 8,
          dailyRate: 50,
          weeklyRate: 300,
          monthlyRate: 1000,
          currency: 'EUR',
          discounts: []
        },
        rules: ['Maintenir l\'espace propre', 'Pas de nourriture', 'Silence requis'],
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: '2',
        name: 'Bureau privé B1',
        description: 'Bureau privé pour 2 personnes avec équipement complet',
        type: SpaceType.PRIVATE_OFFICE,
        capacity: 2,
        location: 'Zone B',
        floor: '1er étage',
        area: 12,
        status: SpaceStatus.AVAILABLE,
        equipment: [
          {
            id: 'eq4',
            name: 'Bureau double',
            type: EquipmentType.DESK,
            quantity: 1,
            status: EquipmentStatus.WORKING
          },
          {
            id: 'eq5',
            name: 'Chaises de bureau',
            type: EquipmentType.CHAIR,
            quantity: 2,
            status: EquipmentStatus.WORKING
          },
          {
            id: 'eq6',
            name: 'Armoire de rangement',
            type: EquipmentType.STORAGE,
            quantity: 1,
            status: EquipmentStatus.WORKING
          }
        ],
        amenities: ['WiFi', 'Climatisation', 'Fenêtre', 'Tableau blanc'],
        images: ['/assets/spaces/private-office-1.jpg'],
        availability: {
          isActive: true,
          schedule: {
            monday: { isOpen: true, openTime: '07:00', closeTime: '20:00', breaks: [] },
            tuesday: { isOpen: true, openTime: '07:00', closeTime: '20:00', breaks: [] },
            wednesday: { isOpen: true, openTime: '07:00', closeTime: '20:00', breaks: [] },
            thursday: { isOpen: true, openTime: '07:00', closeTime: '20:00', breaks: [] },
            friday: { isOpen: true, openTime: '07:00', closeTime: '20:00', breaks: [] },
            saturday: { isOpen: true, openTime: '09:00', closeTime: '18:00', breaks: [] },
            sunday: { isOpen: false, openTime: '09:00', closeTime: '17:00', breaks: [] }
          },
          exceptions: [],
          advanceBookingDays: 60,
          minBookingDuration: 120,
          maxBookingDuration: 720,
          bufferTime: 30
        },
        pricing: {
          hourlyRate: 250,
          dailyRate: 1800,
          weeklyRate: 10000,
          monthlyRate: 35000,
          currency: 'MAD ',
          discounts: []
        },
        rules: ['Accès par badge', 'Nettoyage quotidien inclus', 'Pas de visiteurs sans autorisation'],
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-20')
      },
      {
        id: '3',
        name: 'Salle de réunion Alpha',
        description: 'Salle de réunion moderne pour 8 personnes avec équipement audiovisuel',
        type: SpaceType.MEETING_ROOM,
        capacity: 8,
        location: 'Zone C',
        floor: '2ème étage',
        area: 25,
        status: SpaceStatus.AVAILABLE,
        equipment: [
          {
            id: 'eq7',
            name: 'Table de conférence',
            type: EquipmentType.DESK,
            quantity: 1,
            status: EquipmentStatus.WORKING
          },
          {
            id: 'eq8',
            name: 'Chaises de conférence',
            type: EquipmentType.CHAIR,
            quantity: 8,
            status: EquipmentStatus.WORKING
          },
          {
            id: 'eq9',
            name: 'Projecteur 4K',
            type: EquipmentType.PROJECTOR,
            brand: 'Epson',
            model: 'EB-PU1007',
            quantity: 1,
            status: EquipmentStatus.WORKING
          },
          {
            id: 'eq10',
            name: 'Écran de projection',
            type: EquipmentType.TV_SCREEN,
            quantity: 1,
            status: EquipmentStatus.WORKING
          },
          {
            id: 'eq11',
            name: 'Système audio',
            type: EquipmentType.SPEAKERS,
            quantity: 1,
            status: EquipmentStatus.WORKING
          },
          {
            id: 'eq12',
            name: 'Tableau blanc',
            type: EquipmentType.WHITEBOARD,
            quantity: 2,
            status: EquipmentStatus.WORKING
          }
        ],
        amenities: ['WiFi haut débit', 'Climatisation', 'Éclairage modulable', 'Isolation phonique'],
        images: ['/assets/spaces/meeting-room-1.jpg'],
        availability: {
          isActive: true,
          schedule: {
            monday: { isOpen: true, openTime: '08:00', closeTime: '19:00', breaks: [{ start: '12:00', end: '13:00', reason: 'Nettoyage' }] },
            tuesday: { isOpen: true, openTime: '08:00', closeTime: '19:00', breaks: [{ start: '12:00', end: '13:00', reason: 'Nettoyage' }] },
            wednesday: { isOpen: true, openTime: '08:00', closeTime: '19:00', breaks: [{ start: '12:00', end: '13:00', reason: 'Nettoyage' }] },
            thursday: { isOpen: true, openTime: '08:00', closeTime: '19:00', breaks: [{ start: '12:00', end: '13:00', reason: 'Nettoyage' }] },
            friday: { isOpen: true, openTime: '08:00', closeTime: '19:00', breaks: [{ start: '12:00', end: '13:00', reason: 'Nettoyage' }] },
            saturday: { isOpen: true, openTime: '09:00', closeTime: '17:00', breaks: [] },
            sunday: { isOpen: false, openTime: '09:00', closeTime: '17:00', breaks: [] }
          },
          exceptions: [],
          advanceBookingDays: 90,
          minBookingDuration: 30,
          maxBookingDuration: 480,
          bufferTime: 15
        },
        pricing: {
          hourlyRate: 450,
          dailyRate: 3200,
          weeklyRate: 18000,
          monthlyRate: 60000,
          currency: 'MAD ',
          discounts: []
        },
        rules: ['Réservation obligatoire', 'Nettoyage après usage', 'Matériel à remettre en place'],
        createdAt: new Date('2024-01-05'),
        updatedAt: new Date('2024-01-25')
      },
      {
        id: '4',
        name: 'Espace Collaboratif Principal',
        type: SpaceType.COLLABORATIVE,
        capacity: 40,
        location: 'Rez-de-chaussée',
        floor: 'RDC',
        area: 120,
        status: SpaceStatus.AVAILABLE,
        description: 'Grand espace ouvert pour le travail collaboratif, accessible sans réservation',
        images: [
          'https://via.placeholder.com/800x600/3498db/ffffff?text=Espace+Collaboratif',
          'https://via.placeholder.com/800x600/2980b9/ffffff?text=Zone+Travail'
        ],
        equipment: [
          {
            id: 'eq-collab-1',
            name: 'WiFi haut débit',
            type: EquipmentType.WIFI,
            quantity: 1,
            status: EquipmentStatus.WORKING,
            description: 'Connexion internet haute vitesse'
          },
          {
            id: 'eq-collab-2',
            name: 'Imprimante partagée',
            type: EquipmentType.PRINTER,
            quantity: 1,
            status: EquipmentStatus.WORKING,
            description: 'Imprimante multifonction'
          }
        ],
        amenities: [
          'WiFi haut débit',
          'Prises électriques multiples',
          'Éclairage naturel',
          'Mobilier ergonomique',
          'Espaces détente',
          'Machine à café',
          'Imprimante partagée'
        ],

        availability: {
          isActive: true,
          schedule: {
            monday: { isOpen: true, openTime: '08:00', closeTime: '20:00', breaks: [] },
            tuesday: { isOpen: true, openTime: '08:00', closeTime: '20:00', breaks: [] },
            wednesday: { isOpen: true, openTime: '08:00', closeTime: '20:00', breaks: [] },
            thursday: { isOpen: true, openTime: '08:00', closeTime: '20:00', breaks: [] },
            friday: { isOpen: true, openTime: '08:00', closeTime: '20:00', breaks: [] },
            saturday: { isOpen: true, openTime: '09:00', closeTime: '18:00', breaks: [] },
            sunday: { isOpen: true, openTime: '10:00', closeTime: '16:00', breaks: [] }
          },
          exceptions: [],
          advanceBookingDays: 30,
          minBookingDuration: 0,
          maxBookingDuration: 0,
          bufferTime: 0
        },
        pricing: {
          hourlyRate: 0,
          dailyRate: 0,
          weeklyRate: 0,
          monthlyRate: 0,
          currency: 'MAD ',
          discounts: []
        },
        rules: ['Accès libre pendant les heures d\'ouverture', 'Respect du silence relatif', 'Nettoyage après usage'],
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-30')
      },
      {
        id: '5',
        name: 'Espace Collaboratif Créatif',
        type: SpaceType.COLLABORATIVE,
        capacity: 25,
        location: 'Étage 1',
        floor: '1er',
        area: 80,
        status: SpaceStatus.AVAILABLE,
        description: 'Espace collaboratif dédié aux activités créatives et au brainstorming',
        images: [
          'https://via.placeholder.com/800x600/e74c3c/ffffff?text=Espace+Creatif',
          'https://via.placeholder.com/800x600/c0392b/ffffff?text=Zone+Innovation'
        ],
        equipment: [
          {
            id: 'eq-creat-1',
            name: 'Projecteur créatif',
            type: EquipmentType.PROJECTOR,
            quantity: 1,
            status: EquipmentStatus.WORKING,
            description: 'Projecteur pour présentations créatives'
          },
          {
            id: 'eq-creat-2',
            name: 'Tableaux blancs',
            type: EquipmentType.WHITEBOARD,
            quantity: 4,
            status: EquipmentStatus.WORKING,
            description: 'Tableaux blancs muraux'
          }
        ],
        amenities: [
          'Tableaux blancs muraux',
          'Matériel de prototypage',
          'Éclairage créatif modulable',
          'Mobilier flexible',
          'Espace présentation',
          'Matériel artistique',
          'Zone détente'
        ],

        availability: {
          isActive: true,
          schedule: {
            monday: { isOpen: true, openTime: '08:00', closeTime: '20:00', breaks: [] },
            tuesday: { isOpen: true, openTime: '08:00', closeTime: '20:00', breaks: [] },
            wednesday: { isOpen: true, openTime: '08:00', closeTime: '20:00', breaks: [] },
            thursday: { isOpen: true, openTime: '08:00', closeTime: '20:00', breaks: [] },
            friday: { isOpen: true, openTime: '08:00', closeTime: '20:00', breaks: [] },
            saturday: { isOpen: true, openTime: '09:00', closeTime: '18:00', breaks: [] },
            sunday: { isOpen: true, openTime: '10:00', closeTime: '16:00', breaks: [] }
          },
          exceptions: [],
          advanceBookingDays: 30,
          minBookingDuration: 0,
          maxBookingDuration: 0,
          bufferTime: 0
        },
        pricing: {
          hourlyRate: 0,
          dailyRate: 0,
          weeklyRate: 0,
          monthlyRate: 0,
          currency: 'MAD ',
          discounts: []
        },
        rules: ['Accès libre', 'Matériel à remettre en place', 'Créativité encouragée'],
        createdAt: new Date('2024-01-08'),
        updatedAt: new Date('2024-01-28')
      }
    ];

    const today = new Date();
    const mockReservations: SpaceReservation[] = [
      {
        id: 'res1',
        spaceId: '3',
        spaceName: 'Salle de réunion Alpha',
        userId: 'user1',
        userName: 'Jean Dupont',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 9, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 11, 0),
        status: ReservationStatus.CONFIRMED,
        purpose: 'Réunion équipe marketing',
        attendees: 6,
        notes: 'Présentation client importante',
        totalCost: 90,
        createdAt: new Date('2024-02-10'),
        updatedAt: new Date('2024-02-10')
      },
      {
        id: 'res2',
        spaceId: '2',
        spaceName: 'Bureau privé B1',
        userId: 'user2',
        userName: 'Marie Martin',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 2, 14, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 2, 18, 0),
        status: ReservationStatus.CONFIRMED,
        purpose: 'Travail concentré',
        attendees: 1,
        totalCost: 100,
        createdAt: new Date('2024-02-12'),
        updatedAt: new Date('2024-02-12')
      },
      {
        id: 'res3',
        spaceId: '3',
        spaceName: 'Salle de réunion Alpha',
        userId: 'user3',
        userName: 'Ahmed Benali',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 3, 10, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 3, 12, 0),
        status: ReservationStatus.PENDING,
        purpose: 'Formation équipe',
        attendees: 8,
        notes: 'Besoin projecteur',
        totalCost: 180,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'res4',
        spaceId: '1',
        spaceName: 'Poste de travail P1',
        userId: 'user4',
        userName: 'Sophie Dubois',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 8, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 17, 0),
        status: ReservationStatus.CONFIRMED,
        purpose: 'Travail quotidien',
        attendees: 1,
        totalCost: 225,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'res5',
        spaceId: '3',
        spaceName: 'Salle de réunion Alpha',
        userId: 'user5',
        userName: 'Karim Alami',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() - 1, 15, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() - 1, 17, 0),
        status: ReservationStatus.CANCELLED,
        purpose: 'Réunion client',
        attendees: 4,
        notes: 'Annulée par le client',
        totalCost: 180,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'res6',
        spaceId: '2',
        spaceName: 'Bureau privé B1',
        userId: 'user6',
        userName: 'Fatima Zahra',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 5, 9, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 5, 13, 0),
        status: ReservationStatus.PENDING,
        purpose: 'Entretiens recrutement',
        attendees: 2,
        totalCost: 200,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'res7',
        spaceId: '1',
        spaceName: 'Poste de travail P1',
        userId: 'user7',
        userName: 'Houssam Bennani',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 7, 10, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 7, 16, 0),
        status: ReservationStatus.CONFIRMED,
        purpose: 'Développement projet',
        attendees: 1,
        totalCost: 150,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    this.spacesSubject.next(mockSpaces);
    this.reservationsSubject.next(mockReservations);
  }

  // Méthodes CRUD pour les espaces
  getSpaces(): Observable<Space[]> {
    return this.spaces$.pipe(delay(500));
  }

  getSpaceById(id: string): Observable<Space | null> {
    return this.spaces$.pipe(
      map(spaces => spaces.find(space => space.id === id) || null),
      delay(300)
    );
  }

  createSpace(request: CreateSpaceRequest): Observable<Space> {
    const newSpace: Space = {
      id: Date.now().toString(),
      ...request,
      status: SpaceStatus.AVAILABLE,
      images: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const currentSpaces = this.spacesSubject.value;
    this.spacesSubject.next([...currentSpaces, newSpace]);

    return of(newSpace).pipe(delay(500));
  }

  updateSpace(request: UpdateSpaceRequest): Observable<Space> {
    const currentSpaces = this.spacesSubject.value;
    const index = currentSpaces.findIndex(space => space.id === request.id);

    if (index === -1) {
      throw new Error('Espace non trouvé');
    }

    const updatedSpace: Space = {
      ...currentSpaces[index],
      ...request,
      updatedAt: new Date()
    };

    const updatedSpaces = [...currentSpaces];
    updatedSpaces[index] = updatedSpace;
    this.spacesSubject.next(updatedSpaces);

    return of(updatedSpace).pipe(delay(500));
  }

  deleteSpace(id: string): Observable<boolean> {
    const currentSpaces = this.spacesSubject.value;
    const filteredSpaces = currentSpaces.filter(space => space.id !== id);
    this.spacesSubject.next(filteredSpaces);

    return of(true).pipe(delay(300));
  }

  searchSpaces(filters: SpaceSearchFilters): Observable<Space[]> {
    return this.spaces$.pipe(
      map(spaces => {
        return spaces.filter(space => {
          if (filters.type && space.type !== filters.type) return false;
          if (filters.capacity && space.capacity < filters.capacity) return false;
          if (filters.location && !space.location.toLowerCase().includes(filters.location.toLowerCase())) return false;
          if (filters.floor && space.floor !== filters.floor) return false;
          if (filters.equipment && filters.equipment.length > 0) {
            const spaceEquipmentTypes = space.equipment.map(eq => eq.type);
            if (!filters.equipment.every(type => spaceEquipmentTypes.includes(type))) return false;
          }
          if (filters.amenities && filters.amenities.length > 0) {
            if (!filters.amenities.every(amenity =>
              space.amenities.some(spaceAmenity =>
                spaceAmenity.toLowerCase().includes(amenity.toLowerCase())
              )
            )) return false;
          }
          if (filters.priceRange) {
            if (space.pricing.hourlyRate < filters.priceRange.min ||
                space.pricing.hourlyRate > filters.priceRange.max) return false;
          }
          return true;
        });
      }),
      delay(400)
    );
  }

  // Méthodes pour les réservations
  getReservations(): Observable<SpaceReservation[]> {
    return this.reservations$.pipe(delay(300));
  }

  getReservationsBySpace(spaceId: string): Observable<SpaceReservation[]> {
    return this.reservations$.pipe(
      map(reservations => reservations.filter(res => res.spaceId === spaceId)),
      delay(300)
    );
  }

  // Méthodes pour le planning visuel
  getCalendarEvents(startDate: Date, endDate: Date): Observable<CalendarEvent[]> {
    return this.reservations$.pipe(
      map(reservations => {
        return reservations
          .filter(res => res.startTime >= startDate && res.endTime <= endDate)
          .map(res => ({
            id: res.id,
            title: `${res.spaceName} - ${res.userName}`,
            start: res.startTime,
            end: res.endTime,
            resourceId: res.spaceId,
            color: this.getStatusColor(res.status),
            status: res.status,
            attendees: res.attendees,
            userName: res.userName
          }));
      }),
      delay(300)
    );
  }

  getCalendarResources(): Observable<CalendarResource[]> {
    return this.spaces$.pipe(
      map(spaces => {
        return spaces.map(space => ({
          id: space.id,
          title: space.name,
          type: space.type,
          capacity: space.capacity,
          location: space.location,
          status: space.status
        }));
      }),
      delay(300)
    );
  }

  private getStatusColor(status: ReservationStatus): string {
    switch (status) {
      case ReservationStatus.CONFIRMED:
        return '#52c41a';
      case ReservationStatus.PENDING:
        return '#faad14';
      case ReservationStatus.CANCELLED:
        return '#ff4d4f';
      case ReservationStatus.COMPLETED:
        return '#1890ff';
      case ReservationStatus.NO_SHOW:
        return '#f5222d';
      default:
        return '#d9d9d9';
    }
  }
}
