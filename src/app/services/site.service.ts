import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { Site, SiteStats } from '../models/site.model';

@Injectable({
  providedIn: 'root'
})
export class SiteService {
  private currentSiteSubject = new BehaviorSubject<Site | null>(null);
  public currentSite$ = this.currentSiteSubject.asObservable();

  private sitesSubject = new BehaviorSubject<Site[]>([]);
  public sites$ = this.sitesSubject.asObservable();

  constructor() {
    this.loadMockData();
  }

  private loadMockData() {
    const mockSites: Site[] = [
      {
        id: '1',
        name: 'Workeem Paris',
        city: 'Paris',
        country: 'France',
        address: '123 Avenue des Champs-Élysées, 75008 Paris',
        image: 'assets/images/sites/paris.jpg',
        isActive: true,
        spacesCount: 25,
        membersCount: 150,
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2024-12-01')
      },
      {
        id: '2',
        name: 'Workeem Lyon',
        city: 'Lyon',
        country: 'France',
        address: '45 Rue de la République, 69002 Lyon',
        image: 'assets/images/sites/lyon.jpg',
        isActive: true,
        spacesCount: 18,
        membersCount: 95,
        createdAt: new Date('2023-03-20'),
        updatedAt: new Date('2024-11-28')
      },
      {
        id: '3',
        name: 'Workeem Marseille',
        city: 'Marseille',
        country: 'France',
        address: '78 La Canebière, 13001 Marseille',
        image: 'assets/images/sites/marseille.jpg',
        isActive: true,
        spacesCount: 22,
        membersCount: 120,
        createdAt: new Date('2023-06-10'),
        updatedAt: new Date('2024-12-02')
      },
      {
        id: '4',
        name: 'Workeem Bordeaux',
        city: 'Bordeaux',
        country: 'France',
        address: '12 Cours de l\'Intendance, 33000 Bordeaux',
        image: 'assets/images/sites/bordeaux.jpg',
        isActive: false,
        spacesCount: 15,
        membersCount: 75,
        createdAt: new Date('2023-09-05'),
        updatedAt: new Date('2024-11-15')
      }
    ];

    this.sitesSubject.next(mockSites);
    // Définir Paris comme site actuel par défaut
    this.currentSiteSubject.next(mockSites[0]);
  }

  getSites(): Observable<Site[]> {
    return this.sites$;
  }

  getCurrentSite(): Observable<Site | null> {
    return this.currentSite$;
  }

  switchToSite(siteId: string): void {
    const sites = this.sitesSubject.value;
    const site = sites.find(s => s.id === siteId);
    if (site) {
      this.currentSiteSubject.next(site);
    }
  }

  addSite(site: Omit<Site, 'id' | 'createdAt' | 'updatedAt'>): Observable<Site> {
    const newSite: Site = {
      ...site,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const currentSites = this.sitesSubject.value;
    this.sitesSubject.next([...currentSites, newSite]);

    return of(newSite);
  }

  updateSite(siteId: string, updates: Partial<Site>): Observable<Site | null> {
    const sites = this.sitesSubject.value;
    const siteIndex = sites.findIndex(s => s.id === siteId);
    
    if (siteIndex === -1) {
      return of(null);
    }

    const updatedSite = {
      ...sites[siteIndex],
      ...updates,
      updatedAt: new Date()
    };

    sites[siteIndex] = updatedSite;
    this.sitesSubject.next([...sites]);

    // Mettre à jour le site actuel si c'est celui qui a été modifié
    if (this.currentSiteSubject.value?.id === siteId) {
      this.currentSiteSubject.next(updatedSite);
    }

    return of(updatedSite);
  }

  deleteSite(siteId: string): Observable<boolean> {
    const sites = this.sitesSubject.value;
    const filteredSites = sites.filter(s => s.id !== siteId);
    
    if (filteredSites.length === sites.length) {
      return of(false); // Site non trouvé
    }

    this.sitesSubject.next(filteredSites);

    // Si le site supprimé était le site actuel, basculer vers le premier site disponible
    if (this.currentSiteSubject.value?.id === siteId) {
      const firstSite = filteredSites.length > 0 ? filteredSites[0] : null;
      this.currentSiteSubject.next(firstSite);
    }

    return of(true);
  }

  getSiteStats(siteId: string): Observable<SiteStats> {
    // Mock data pour les statistiques
    const mockStats: SiteStats = {
      totalSpaces: 25,
      totalMembers: 150,
      occupancyRate: 85,
      monthlyRevenue: 12500
    };

    return of(mockStats);
  }
}
