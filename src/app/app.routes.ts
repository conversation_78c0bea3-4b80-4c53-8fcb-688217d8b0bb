import { Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';

export const routes: Routes = [
  // Route de connexion (non protégée)
  {
    path: 'login',
    loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent)
  },

  // Redirection par défaut vers welcome (protégée)
  {
    path: '',
    pathMatch: 'full',
    redirectTo: '/welcome'
  },

  // Routes protégées par le guard d'authentification
  {
    path: 'welcome',
    loadChildren: () => import('./pages/welcome/welcome.routes').then(m => m.WELCOME_ROUTES),
    canActivate: [AuthGuard]
  },
  {
    path: 'members',
    loadChildren: () => import('./pages/members/members.routes').then(m => m.MEMBERS_ROUTES),
    canActivate: [AuthGuard]
  },
  {
    path: 'subscriptions',
    loadChildren: () => import('./pages/subscriptions/subscriptions.routes').then(m => m.SUBSCRIPTIONS_ROUTES),
    canActivate: [AuthGuard]
  },
  {
    path: 'spaces',
    loadChildren: () => import('./pages/spaces/spaces.routes').then(m => m.SPACES_ROUTES),
    canActivate: [AuthGuard]
  },
  {
    path: 'reservations',
    loadComponent: () => import('./pages/reservations/reservations.component').then(m => m.ReservationsComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'statistics',
    loadComponent: () => import('./pages/statistics/statistics.component').then(m => m.StatisticsComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'reservation-form',
    loadComponent: () => import('./pages/reservation-form/reservation-form.component').then(m => m.ReservationFormComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'reservations/:id',
    loadComponent: () => import('./pages/reservation-details/reservation-details.component').then(m => m.ReservationDetailsComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'billing',
    loadComponent: () => import('./pages/billing/billing.component').then(m => m.BillingComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'billing/new',
    loadComponent: () => import('./pages/billing-form/billing-form.component').then(m => m.BillingFormComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'billing/edit/:id',
    loadComponent: () => import('./pages/billing-form/billing-form.component').then(m => m.BillingFormComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'billing/:id',
    loadComponent: () => import('./pages/billing-details/billing-details.component').then(m => m.BillingDetailsComponent),
    canActivate: [AuthGuard]
  },

  // Route wildcard pour les pages non trouvées
  {
    path: '**',
    redirectTo: '/welcome'
  }
];
