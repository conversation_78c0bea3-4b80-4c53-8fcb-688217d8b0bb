import { Component, OnInit, OnD<PERSON>roy, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SpaceService } from '../../services/space.service';
import { Space } from '../../models/space.model';
import { Reservation } from '../../models/reservation.model';
import { of } from 'rxjs';

interface TimeSlot {
  hour: string;
  time24: string;
}

interface SpaceAvailability {
  space: Space;
  reservations: Reservation[];
}

@Component({
  selector: 'app-daily-calendar',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzIconModule,
    NzTagModule,
    NzToolTipModule
  ],
  templateUrl: './daily-calendar.component.html',
  styleUrls: ['./daily-calendar.component.css']
})
export class DailyCalendarComponent implements OnInit, OnDestroy {
  timeSlots: TimeSlot[] = [];
  spacesAvailability: SpaceAvailability[] = [];
  currentDate = new Date();
  loading = true;

  constructor(
    private spaceService: SpaceService,
    private router: Router,
    private message: NzMessageService
  ) {}

  ngOnInit() {
    this.generateTimeSlots();
    this.loadSpacesAndReservations();
  }

  ngOnDestroy() {
    this.clearLongPressTimer();
  }

  generateTimeSlots() {
    this.timeSlots = [];
    for (let hour = 9; hour <= 18; hour++) {
      // Ajouter l'heure pleine
      const time24 = `${hour.toString().padStart(2, '0')}:00`;
      const time12 = hour <= 12 ? `${hour}:00 AM` : `${hour - 12}:00 PM`;
      if (hour === 12) {
        this.timeSlots.push({
          hour: '12:00 PM',
          time24: time24
        });
      } else {
        this.timeSlots.push({
          hour: time12,
          time24: time24
        });
      }

      // Ajouter la demi-heure (sauf pour la dernière heure)
      if (hour < 18) {
        const time24Half = `${hour.toString().padStart(2, '0')}:30`;
        const time12Half = hour < 12 ? `${hour}:30 AM` : hour === 12 ? '12:30 PM' : `${hour - 12}:30 PM`;
        this.timeSlots.push({
          hour: time12Half,
          time24: time24Half
        });
      }
    }
  }

  loadSpacesAndReservations() {
    this.loading = true;

    // Charger les espaces
    this.spaceService.getSpaces().subscribe({
      next: (spaces: Space[]) => {
        // Simuler des réservations pour le jour actuel
        const mockReservations = this.generateMockReservations();

        // Filtrer les réservations du jour actuel
        const todayReservations = mockReservations.filter((reservation: Reservation) => {
          const reservationDate = new Date(reservation.startTime);
          return this.isSameDay(reservationDate, this.currentDate);
        });

        // Créer la structure de disponibilité
        this.spacesAvailability = spaces.map(space => ({
          space: space,
          reservations: todayReservations.filter((res: Reservation) => res.spaceId === space.id)
        }));

        this.loading = false;
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement des espaces:', error);
        this.loading = false;
      }
    });
  }

  private generateMockReservations(): Reservation[] {
    const today = new Date();
    return [
      {
        id: '1',
        spaceId: '1',
        spaceName: 'Salle de réunion Alpha',
        userId: '1',
        userName: 'Houssam Benali',
        userEmail: '<EMAIL>',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 9, 30),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 11, 0),
        purpose: 'Réunion équipe développement',
        status: 'confirmed',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '2',
        spaceId: '2',
        spaceName: 'Bureau privé B1',
        userId: '2',
        userName: 'Sarah Martin',
        userEmail: '<EMAIL>',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 14, 30),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 16, 30),
        purpose: 'Session de coaching',
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '3',
        spaceId: '3',
        spaceName: 'Espace de coworking C1',
        userId: '3',
        userName: 'Ahmed Tazi',
        userEmail: '<EMAIL>',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 9, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 11, 30),
        purpose: 'Travail sur projet client',
        status: 'cancelled',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '4',
        spaceId: '1',
        spaceName: 'Salle de réunion Alpha',
        userId: '4',
        userName: 'Fatima Zahra',
        userEmail: '<EMAIL>',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 15, 30),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 17, 30),
        purpose: 'Présentation client',
        status: 'confirmed',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  isTimeSlotReserved(space: Space, timeSlot: TimeSlot): Reservation | null {
    const spaceAvailability = this.spacesAvailability.find(sa => sa.space.id === space.id);
    if (!spaceAvailability) return null;

    const [slotHour, slotMinute] = timeSlot.time24.split(':').map(Number);
    const slotTimeInMinutes = slotHour * 60 + slotMinute;

    return spaceAvailability.reservations.find(reservation => {
      // Les dates sont déjà des objets Date
      const startTime = reservation.startTime;
      const endTime = reservation.endTime;

      // Utiliser getHours() et getMinutes() qui donnent l'heure locale
      const startTimeInMinutes = startTime.getHours() * 60 + startTime.getMinutes();
      const endTimeInMinutes = endTime.getHours() * 60 + endTime.getMinutes();

      return slotTimeInMinutes >= startTimeInMinutes && slotTimeInMinutes < endTimeInMinutes;
    }) || null;
  }

  isFirstSlotOfReservation(space: Space, timeSlot: TimeSlot, reservation: Reservation): boolean {
    // Les dates sont déjà des objets Date
    const startTime = reservation.startTime;
    const [slotHour, slotMinute] = timeSlot.time24.split(':').map(Number);

    return startTime.getHours() === slotHour && startTime.getMinutes() === slotMinute;
  }

  getReservationDurationInSlots(reservation: Reservation): number {
    // Les dates sont déjà des objets Date
    const startTime = reservation.startTime;
    const endTime = reservation.endTime;

    const startTimeInMinutes = startTime.getHours() * 60 + startTime.getMinutes();
    const endTimeInMinutes = endTime.getHours() * 60 + endTime.getMinutes();

    return Math.ceil((endTimeInMinutes - startTimeInMinutes) / 30); // 30 minutes par slot
  }

  getReservationTopOffset(reservation: Reservation): number {
    // Calculer l'offset en pixels depuis le début du créneau
    const startTime = reservation.startTime;
    const startMinutes = startTime.getMinutes();

    // Hauteur adaptative selon la taille d'écran
    const slotHeight = this.getSlotHeight();

    // Si la réservation commence à 15 minutes dans le créneau, offset de 50%
    // Si elle commence à 30 minutes, c'est le créneau suivant
    const offsetPercentage = (startMinutes % 30) / 30;
    return offsetPercentage * slotHeight;
  }

  getSlotHeight(): number {
    // Détection mobile basique
    return window.innerWidth <= 768 ? 60 : 40;
  }

  getReservationHeight(reservation: Reservation): number {
    const slots = this.getReservationDurationInSlots(reservation);
    const slotHeight = this.getSlotHeight();
    return slots * slotHeight;
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    // Force la re-détection des hauteurs lors du redimensionnement
    // Angular va automatiquement recalculer les styles
  }

  @HostListener('document:mouseup', ['$event'])
  @HostListener('document:touchend', ['$event'])
  onDocumentMouseUp(event: any) {
    if (this.isDragging) {
      this.resetDragState();
    }
  }

  getReservationStatus(reservation: Reservation): string {
    switch (reservation.status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  }

  getReservationColor(reservation: Reservation): string {
    switch (reservation.status) {
      case 'confirmed':
        return '#6E56CF'; // Violet principal pour confirmé
      case 'pending':
        return '#B8A9E8'; // Violet soft pour en attente
      case 'cancelled':
        return '#C7C7CC'; // Gris plus clair pour annulé
      default:
        return '#d9d9d9';
    }
  }

  getSpaceIcon(spaceType: string): string {
    switch (spaceType) {
      case 'meeting_room':
        return 'team';
      case 'office':
        return 'home';
      case 'desk':
        return 'desktop';
      case 'phone_booth':
        return 'phone';
      default:
        return 'appstore';
    }
  }

  getSpaceCapacityIcon(capacity: number): string {
    if (capacity <= 2) return 'user';
    if (capacity <= 6) return 'team';
    return 'usergroup-add';
  }

  formatReservationTime(reservation: Reservation): string {
    // Les dates sont déjà des objets Date
    const start = reservation.startTime;
    const end = reservation.endTime;

    // Utiliser toLocaleTimeString avec la timezone française
    const formatTime = (date: Date) => {
      return date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    };

    return `${formatTime(start)} - ${formatTime(end)}`;
  }

  getReservationTooltip(reservation: Reservation): string {
    return `${reservation.userName}\n${this.formatReservationTime(reservation)}\nStatut: ${this.getStatusLabel(reservation.status)}`;
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'Confirmé';
      case 'pending':
        return 'En attente';
      case 'cancelled':
        return 'Annulé';
      default:
        return 'Inconnu';
    }
  }

  refreshCalendar() {
    this.loadSpacesAndReservations();
  }

  // Variables pour le glisser-déposer
  private isDragging = false;
  private dragStartSpace: Space | null = null;
  private dragStartTimeSlot: TimeSlot | null = null;
  private dragEndTimeSlot: TimeSlot | null = null;
  selectedSlots: Set<string> = new Set(); // Pour tracker les slots sélectionnés

  // Variables pour le long press sur mobile
  private longPressTimer: any = null;
  private isLongPressActive = false;
  private longPressDelay = 500; // 500ms pour activer le mode sélection
  private touchStartPosition = { x: 0, y: 0 };
  private touchMoveThreshold = 10; // pixels

  // Méthode pour cliquer sur une réservation
  onReservationClick(reservation: Reservation): void {
    this.router.navigate(['/reservations', reservation.id]);
  }

  // Méthodes pour le glisser-déposer
  onSlotMouseDown(space: Space, timeSlot: TimeSlot, event: MouseEvent): void {
    event.preventDefault();
    this.isDragging = true;
    this.dragStartSpace = space;
    this.dragStartTimeSlot = timeSlot;
    this.dragEndTimeSlot = timeSlot;
    this.updateSelectedSlots();
  }

  onSlotMouseOver(space: Space, timeSlot: TimeSlot, event: MouseEvent): void {
    if (this.isDragging && this.dragStartSpace?.id === space.id) {
      this.dragEndTimeSlot = timeSlot;
      this.updateSelectedSlots();
    }
  }

  onSlotMouseUp(space: Space, timeSlot: TimeSlot, event: MouseEvent): void {
    if (this.isDragging && this.dragStartSpace?.id === space.id) {
      this.isDragging = false;

      if (this.dragStartTimeSlot && this.dragEndTimeSlot) {
        const startTime = this.getTimeSlotDateTime(this.dragStartTimeSlot);
        const endTime = this.getTimeSlotDateTime(this.dragEndTimeSlot);

        // S'assurer que l'heure de fin est après l'heure de début
        const actualStartTime = startTime < endTime ? startTime : endTime;
        const actualEndTime = startTime < endTime ? endTime : startTime;

        // Ajouter 30 minutes à l'heure de fin pour avoir la durée complète
        actualEndTime.setMinutes(actualEndTime.getMinutes() + 30);

        // Vérifier s'il y a des créneaux occupés dans la sélection
        if (this.hasConflictingReservations(space, actualStartTime, actualEndTime)) {
          // Afficher un message d'erreur
          this.message.error('Impossible de créer une réservation qui traverse un créneau déjà occupé.');
          this.resetDragState();
          return;
        }

        // Naviguer vers le formulaire de réservation avec les données pré-remplies
        this.router.navigate(['/reservation-form'], {
          queryParams: {
            spaceId: space.id,
            startTime: actualStartTime.toISOString(),
            endTime: actualEndTime.toISOString()
          }
        });
      }

      // Reset des variables
      this.dragStartSpace = null;
      this.dragStartTimeSlot = null;
      this.dragEndTimeSlot = null;
      this.selectedSlots.clear();
    }
  }

  // Méthodes pour le support tactile (mobile)
  onSlotTouchStart(space: Space, timeSlot: TimeSlot, event: TouchEvent): void {
    // Ne pas empêcher le comportement par défaut immédiatement pour permettre le scroll
    const touch = event.touches[0];
    this.touchStartPosition = { x: touch.clientX, y: touch.clientY };
    this.isLongPressActive = false;

    // Démarrer le timer pour le long press
    this.longPressTimer = setTimeout(() => {
      // Long press détecté - activer le mode sélection
      this.isLongPressActive = true;
      this.isDragging = true;
      this.dragStartSpace = space;
      this.dragStartTimeSlot = timeSlot;
      this.dragEndTimeSlot = timeSlot;
      this.updateSelectedSlots();

      // Feedback haptique si disponible
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }, this.longPressDelay);
  }

  onSlotTouchMove(space: Space, timeSlot: TimeSlot, event: TouchEvent): void {
    const touch = event.touches[0];

    // Calculer la distance de mouvement depuis le début
    const deltaX = Math.abs(touch.clientX - this.touchStartPosition.x);
    const deltaY = Math.abs(touch.clientY - this.touchStartPosition.y);
    const totalMovement = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Si le mouvement total dépasse le seuil avant le long press, annuler le timer
    // Cela permet le scroll libre dans toutes les directions
    if (totalMovement > this.touchMoveThreshold && !this.isLongPressActive) {
      this.clearLongPressTimer();
      return; // Permettre le scroll libre
    }

    // Si le mode sélection est actif, gérer le glissement
    if (this.isDragging && this.isLongPressActive && this.dragStartSpace?.id === space.id) {
      // Empêcher le scroll seulement quand on est en mode sélection
      event.preventDefault();

      // Obtenir les coordonnées du toucher
      if (touch) {
        // Trouver l'élément sous le doigt
        const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
        if (elementBelow) {
          // Trouver le slot correspondant
          const slotElement = elementBelow.closest('.time-cell');
          if (slotElement) {
            // Trouver l'index du slot dans la grille
            const timeCells = Array.from(slotElement.parentElement?.children || []);
            const slotIndex = timeCells.indexOf(slotElement);

            if (slotIndex >= 0 && slotIndex < this.timeSlots.length) {
              this.dragEndTimeSlot = this.timeSlots[slotIndex];
              this.updateSelectedSlots();
            }
          }
        }
      }
    }
  }

  onSlotTouchEnd(space: Space, timeSlot: TimeSlot, event: TouchEvent): void {
    // Nettoyer le timer du long press
    this.clearLongPressTimer();

    // Si le mode sélection était actif, traiter la réservation
    if (this.isDragging && this.isLongPressActive && this.dragStartSpace?.id === space.id) {
      event.preventDefault();
      this.isDragging = false;
      this.isLongPressActive = false;

      if (this.dragStartTimeSlot && this.dragEndTimeSlot) {
        const startTime = this.getTimeSlotDateTime(this.dragStartTimeSlot);
        const endTime = this.getTimeSlotDateTime(this.dragEndTimeSlot);

        // S'assurer que l'heure de fin est après l'heure de début
        const actualStartTime = startTime < endTime ? startTime : endTime;
        const actualEndTime = startTime < endTime ? endTime : startTime;

        // Ajouter 30 minutes à l'heure de fin pour avoir la durée complète
        actualEndTime.setMinutes(actualEndTime.getMinutes() + 30);

        // Vérifier s'il y a des créneaux occupés dans la sélection
        if (this.hasConflictingReservations(space, actualStartTime, actualEndTime)) {
          // Afficher un message d'erreur
          this.message.error('Impossible de créer une réservation qui traverse un créneau déjà occupé.');
          this.resetDragState();
          return;
        }

        // Naviguer vers le formulaire de réservation avec les données pré-remplies
        this.router.navigate(['/reservation-form'], {
          queryParams: {
            spaceId: space.id,
            startTime: actualStartTime.toISOString(),
            endTime: actualEndTime.toISOString()
          }
        });
      }

      // Reset des variables
      this.dragStartSpace = null;
      this.dragStartTimeSlot = null;
      this.dragEndTimeSlot = null;
      this.selectedSlots.clear();
    } else {
      // Si ce n'était pas un long press, juste nettoyer les variables
      this.resetDragState();
    }
  }

  private getTimeSlotDateTime(timeSlot: TimeSlot): Date {
    // Utiliser la date actuelle en heure locale
    const today = new Date();
    const [hours, minutes] = timeSlot.time24.split(':').map(Number);

    // Créer une date en heure locale pour éviter les problèmes de timezone
    return new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes);
  }

  // Méthodes pour la sélection visuelle
  updateSelectedSlots(): void {
    this.selectedSlots.clear();

    if (!this.dragStartSpace || !this.dragStartTimeSlot || !this.dragEndTimeSlot) {
      return;
    }

    const startIndex = this.timeSlots.findIndex(slot => slot.time24 === this.dragStartTimeSlot!.time24);
    const endIndex = this.timeSlots.findIndex(slot => slot.time24 === this.dragEndTimeSlot!.time24);

    const minIndex = Math.min(startIndex, endIndex);
    const maxIndex = Math.max(startIndex, endIndex);

    for (let i = minIndex; i <= maxIndex; i++) {
      const slotKey = `${this.dragStartSpace.id}-${this.timeSlots[i].time24}`;
      this.selectedSlots.add(slotKey);
    }
  }

  isSlotSelected(space: Space, timeSlot: TimeSlot): boolean {
    const slotKey = `${space.id}-${timeSlot.time24}`;
    return this.selectedSlots.has(slotKey);
  }

  // Méthodes utilitaires pour le long press
  private clearLongPressTimer(): void {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  private resetDragState(): void {
    this.isDragging = false;
    this.isLongPressActive = false;
    this.dragStartSpace = null;
    this.dragStartTimeSlot = null;
    this.dragEndTimeSlot = null;
    this.selectedSlots.clear();
    this.clearLongPressTimer();
  }

  // Vérifier s'il y a des conflits de réservation dans la plage sélectionnée
  private hasConflictingReservations(space: Space, startTime: Date, endTime: Date): boolean {
    const spaceAvailability = this.spacesAvailability.find(sa => sa.space.id === space.id);
    if (!spaceAvailability) return false;

    // Convertir les heures en minutes pour faciliter la comparaison
    const selectionStartMinutes = startTime.getHours() * 60 + startTime.getMinutes();
    const selectionEndMinutes = endTime.getHours() * 60 + endTime.getMinutes();

    // Vérifier chaque réservation existante
    for (const reservation of spaceAvailability.reservations) {
      const reservationStartMinutes = reservation.startTime.getHours() * 60 + reservation.startTime.getMinutes();
      const reservationEndMinutes = reservation.endTime.getHours() * 60 + reservation.endTime.getMinutes();

      // Vérifier s'il y a un chevauchement
      // Il y a conflit si la sélection commence avant la fin de la réservation
      // ET si la sélection se termine après le début de la réservation
      if (selectionStartMinutes < reservationEndMinutes && selectionEndMinutes > reservationStartMinutes) {
        return true;
      }
    }

    return false;
  }
}
