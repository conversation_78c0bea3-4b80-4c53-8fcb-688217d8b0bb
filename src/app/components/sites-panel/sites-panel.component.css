/* Backdrop */
.sites-backdrop {
  position: fixed;
  top: 64px; /* Commence après le header */
  left: 280px; /* Commence après la sidebar normale */
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 999;
  backdrop-filter: blur(2px);
  transition: left 0.2s ease; /* Animation pour le changement de position */
}

.sites-backdrop.sidebar-collapsed {
  left: 80px; /* Position quand la sidebar est réduite */
}

.sites-panel {
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 1000;
}

/* Bouton de toggle */
.sites-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.sites-toggle:hover {
  background: #fafafa;
}

.current-site-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.current-site-info nz-icon {
  color: #6E56CF;
  font-size: 16px;
}

.site-name {
  font-weight: 600;
  color: #1C1C1E;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 1;
}

.site-city {
  color: #8E8E93;
  font-size: 14px;
  white-space: nowrap;
  flex-shrink: 0;
}

.toggle-icon {
  color: #8E8E93;
  transition: transform 0.3s ease;
}

.sites-panel.expanded .toggle-icon {
  transform: rotate(180deg);
}

/* Contenu du panel */
.sites-content {
  padding: 20px 24px 24px 24px; /* Padding top augmenté pour éviter la coupure en hover */
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.sites-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.sites-header h3 {
  margin: 0;
  color: #1C1C1E;
  font-size: 16px;
  font-weight: 600;
}

/* Grille des sites */
.sites-grid {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding: 4px 0 8px 0; /* Padding top pour éviter la coupure en hover */
  scrollbar-width: thin;
  scrollbar-color: #E5E5EA transparent;
}

.sites-grid::-webkit-scrollbar {
  height: 6px;
}

.sites-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sites-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.site-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-width: 160px;
  flex-shrink: 0;
}

.site-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.site-card.active {
  border-color: #6E56CF;
  background: linear-gradient(135deg, #f8f6ff 0%, #ffffff 100%);
}

.site-card.inactive {
  opacity: 0.6;
  cursor: not-allowed;
}

.site-card.inactive:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* Carte d'ajout de site */
.add-site-card {
  border: 2px dashed #d9d9d9 !important;
  background: #fafafa !important;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  transition: all 0.3s ease;
  order: 999; /* Place la carte d'ajout à la fin */
}

.add-site-card:hover {
  border-color: #6E56CF !important;
  background: #f8f6ff !important;
  transform: translateY(-2px);
}

.add-site-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #8E8E93;
}

.add-site-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-site-card:hover .add-site-icon {
  border-color: #6E56CF;
  background: #6E56CF;
  color: #ffffff;
}

.add-site-card:hover .add-site-icon nz-icon {
  color: #ffffff;
}

.add-site-icon nz-icon {
  font-size: 16px;
  color: #8E8E93;
  transition: color 0.3s ease;
}

.add-site-text {
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.add-site-card:hover .add-site-text {
  color: #6E56CF;
}

/* Icône du site */
.site-icon {
  position: relative;
  width: 100%;
  height: 80px;
  border-radius: 6px;
  background: linear-gradient(135deg, #6E56CF 0%, #8B7ED8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.site-icon nz-icon {
  font-size: 32px;
  color: #ffffff;
}

.site-status {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.site-status.active nz-icon {
  color: #52c41a;
  font-size: 12px;
}

.site-status:not(.active) nz-icon {
  color: #faad14;
  font-size: 12px;
}

/* Informations du site */
.site-info h4 {
  margin: 0 0 6px 0;
  color: #1C1C1E;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.site-location {
  margin: 0 0 8px 0;
  color: #8E8E93;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Styles des stats supprimés car non utilisés */

/* Actions du site */
.site-actions {
  position: absolute;
  top: 12px;
  right: 12px;
}

.site-actions button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modal */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* Menu dropdown */
:host ::ng-deep .ant-dropdown-menu-item.danger {
  color: #ff4d4f;
}

:host ::ng-deep .ant-dropdown-menu-item.danger:hover {
  background-color: #fff2f0;
}

/* Responsive */
@media (max-width: 768px) {
  /* Backdrop mobile */
  .sites-backdrop,
  .sites-backdrop.sidebar-collapsed {
    top: 56px; /* Header mobile plus petit */
    left: 0; /* Pas de sidebar sur mobile */
  }

  .sites-toggle {
    padding: 16px;
  }

  .sites-content {
    padding: 16px;
  }

  .sites-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .sites-grid {
    gap: 12px;
    padding: 0 16px 8px 16px;
  }

  .site-card {
    padding: 12px;
    min-width: 140px;
  }

  .site-icon {
    height: 70px;
  }

  .site-icon nz-icon {
    font-size: 28px;
  }

  .add-site-card {
    min-height: 100px;
  }

  .add-site-icon {
    width: 28px;
    height: 28px;
  }

  .add-site-icon nz-icon {
    font-size: 14px;
  }

  .current-site-info {
    gap: 8px;
  }

  .site-name {
    font-size: 14px;
  }

  .site-city {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .sites-toggle {
    padding: 12px 16px;
  }

  .current-site-info {
    gap: 8px;
  }

  .site-name {
    font-size: 14px;
  }

  .site-city {
    font-size: 12px;
  }

  .site-card {
    min-width: 120px;
  }

  .site-icon {
    height: 60px;
  }

  .site-icon nz-icon {
    font-size: 24px;
  }

  .add-site-card {
    min-height: 80px;
  }
}
