import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzMenuModule } from 'ng-zorro-antd/menu';

import { SiteService } from '../../services/site.service';
import { Site } from '../../models/site.model';
import { slideDownAnimation, fadeInAnimation } from '../../animations/slide.animation';

@Component({
  selector: 'app-sites-panel',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzIconModule,
    NzDropDownModule,
    NzModalModule,
    NzFormModule,
    NzInputModule,
    NzSwitchModule,
    NzGridModule,
    NzMenuModule
  ],
  templateUrl: './sites-panel.component.html',
  styleUrls: ['./sites-panel.component.css'],
  animations: [slideDownAnimation, fadeInAnimation]
})
export class SitesPanelComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  @Input() sidebarCollapsed = false;

  isExpanded = false;
  sites: Site[] = [];
  currentSite: Site | null = null;

  // Modal
  isModalVisible = false;
  modalTitle = '';
  editingSite: Site | null = null;
  saving = false;

  siteForm: FormGroup;

  constructor(
    private siteService: SiteService,
    private fb: FormBuilder,
    private message: NzMessageService
  ) {
    this.siteForm = this.fb.group({
      name: ['', [Validators.required]],
      city: ['', [Validators.required]],
      country: ['', [Validators.required]],
      address: ['', [Validators.required]],
      isActive: [true],
      spacesCount: [0],
      membersCount: [0]
    });
  }

  ngOnInit() {
    // Charger les sites
    this.siteService.getSites()
      .pipe(takeUntil(this.destroy$))
      .subscribe(sites => {
        this.sites = sites;
      });

    // Écouter le site actuel
    this.siteService.getCurrentSite()
      .pipe(takeUntil(this.destroy$))
      .subscribe(site => {
        this.currentSite = site;
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  togglePanel() {
    this.isExpanded = !this.isExpanded;
  }

  closePanel() {
    this.isExpanded = false;
  }

  selectSite(site: Site) {
    if (site.isActive) {
      this.siteService.switchToSite(site.id);
      this.closePanel();
      this.message.success(`Basculé vers ${site.name}`);
    } else {
      this.message.warning('Ce site est désactivé');
    }
  }

  openAddSiteModal() {
    this.modalTitle = 'Ajouter un nouveau site';
    this.editingSite = null;
    this.siteForm.reset({
      name: '',
      city: '',
      country: '',
      address: '',
      isActive: true,
      spacesCount: 0,
      membersCount: 0
    });
    this.isModalVisible = true;
  }

  editSite(site: Site) {
    this.modalTitle = 'Modifier le site';
    this.editingSite = site;
    this.siteForm.patchValue({
      name: site.name,
      city: site.city,
      country: site.country,
      address: site.address,
      isActive: site.isActive,
      spacesCount: site.spacesCount,
      membersCount: site.membersCount
    });
    this.isModalVisible = true;
  }

  saveSite() {
    if (this.siteForm.valid) {
      this.saving = true;
      const formValue = this.siteForm.value;

      if (this.editingSite) {
        // Modification
        this.siteService.updateSite(this.editingSite.id, formValue)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (updatedSite) => {
              if (updatedSite) {
                this.message.success('Site modifié avec succès');
                this.closeModal();
              }
            },
            error: () => {
              this.message.error('Erreur lors de la modification du site');
              this.saving = false;
            }
          });
      } else {
        // Ajout
        this.siteService.addSite(formValue)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (newSite) => {
              this.message.success('Site ajouté avec succès');
              this.closeModal();
            },
            error: () => {
              this.message.error('Erreur lors de l\'ajout du site');
              this.saving = false;
            }
          });
      }
    }
  }

  toggleSiteStatus(site: Site) {
    this.siteService.updateSite(site.id, { isActive: !site.isActive })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedSite) => {
          if (updatedSite) {
            const status = updatedSite.isActive ? 'activé' : 'désactivé';
            this.message.success(`Site ${status} avec succès`);
          }
        },
        error: () => {
          this.message.error('Erreur lors de la modification du statut');
        }
      });
  }

  deleteSite(site: Site) {
    if (this.sites.length <= 1) {
      this.message.warning('Vous ne pouvez pas supprimer le dernier site');
      return;
    }

    this.siteService.deleteSite(site.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (success) => {
          if (success) {
            this.message.success('Site supprimé avec succès');
          } else {
            this.message.error('Site non trouvé');
          }
        },
        error: () => {
          this.message.error('Erreur lors de la suppression du site');
        }
      });
  }

  closeModal() {
    this.isModalVisible = false;
    this.saving = false;
    this.editingSite = null;
  }
}
