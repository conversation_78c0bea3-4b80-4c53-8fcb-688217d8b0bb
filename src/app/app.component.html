<!-- Affichage conditionnel : layout complet si authentifié, sinon juste le router-outlet -->
<div *ngIf="!isAuthenticated || isLoginRoute(); else authenticatedLayout">
  <router-outlet></router-outlet>
</div>

<ng-template #authenticatedLayout>
<nz-layout class="app-layout"
  [class.sidebar-collapsed]="isCollapsed"
  [class.sidebar-open]="sidebarOpen && isMobile"
  [class.mobile-layout]="isMobile">
  <nz-sider class="menu-sidebar"
    nzCollapsible
    nzWidth="280px"
    nzBreakpoint="md"
    [(nzCollapsed)]="isCollapsed"
    (nzCollapsedChange)="onSidebarCollapsedChange($event)"
    [nzTrigger]="null"
  >
    <div class="sidebar-logo">
      <div class="logo-container">
        <img [src]="logoSrc" alt="Workeem Logo" class="logo-image" [class.logo-collapsed]="isCollapsed">
      </div>
    </div>

    <ul nz-menu nzTheme="light" nzMode="inline" [nzInlineCollapsed]="isCollapsed" class="main-menu">
      <!-- Tableau de bord -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/welcome">
          <nz-icon nzType="dashboard" nzTheme="outline"></nz-icon>
          <span>Tableau de bord</span>
        </a>
      </li>

      <!-- Gestion des membres -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/members">
          <nz-icon nzType="team" nzTheme="outline"></nz-icon>
          <span>Gestion des membres</span>
        </a>
      </li>

      <!-- Gestion des abonnements & formules -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/subscriptions">
          <nz-icon nzType="credit-card" nzTheme="outline"></nz-icon>
          <span>Abonnements & formules</span>
        </a>
      </li>

      <!-- Gestion des espaces -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/spaces">
          <nz-icon nzType="environment" nzTheme="outline"></nz-icon>
          <span>Gestion des espaces</span>
        </a>
      </li>

      <!-- Réservations -->
      <li nz-menu-item [nzMatchRouter]="true" class="menu-item"
          [class.ant-menu-item-selected]="isReservationRoute()">
        <a routerLink="/reservations">
          <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
          <span>Réservations</span>
        </a>
      </li>

      <!-- Facturation & paiements -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/billing">
          <nz-icon nzType="dollar" nzTheme="outline"></nz-icon>
          <span>Facturation & paiements</span>
        </a>
      </li>

      <!-- Statistiques -->
      <li nz-menu-item nzMatchRouter class="menu-item">
        <a routerLink="/statistics">
          <nz-icon nzType="bar-chart" nzTheme="outline"></nz-icon>
          <span>Statistiques</span>
        </a>
      </li>
    </ul>

    <!-- Tenant info au bas de la sidebar -->
    <div class="sidebar-footer" [class.collapsed]="isCollapsed">
      <div class="tenant-info">
        <div class="tenant-icon">
          <nz-icon nzType="home" nzTheme="outline"></nz-icon>
        </div>
        <div class="tenant-details" *ngIf="!isCollapsed">
          <div class="tenant-name">{{ tenantName }}</div>
          <div class="tenant-type">Espace de coworking</div>
        </div>
      </div>
    </div>
  </nz-sider>

  <!-- Overlay mobile pour fermer le sidebar -->
  <div class="mobile-overlay"
       *ngIf="isMobile && sidebarOpen"
       (click)="closeMobileSidebar()"></div>

  <nz-layout>
    <nz-header>
      <div class="app-header">
        <div class="header-left">
          <!-- Bouton hamburger pour mobile -->
          <span class="header-trigger mobile-trigger"
                *ngIf="isMobile"
                (click)="toggleSidebar()">
            <nz-icon class="trigger" nzType="menu" />
          </span>

          <!-- Bouton collapse/expand pour desktop -->
          <span class="header-trigger desktop-trigger"
                *ngIf="!isMobile"
                (click)="toggleSidebar()">
            <nz-icon class="trigger" [nzType]="isCollapsed ? 'menu-unfold' : 'menu-fold'" />
          </span>
        </div>

        <!-- Titre mobile -->
        <div class="header-center mobile-title" *ngIf="isMobile">
          <h2 class="page-title">{{ getPageTitle() }}</h2>
        </div>

        <!-- Menu de raccourcis au centre - caché sur mobile -->
        <div class="header-center" *ngIf="!isMobile">
          <div class="quick-actions-menu"
               (mouseenter)="showQuickActionsMenu()"
               (mouseleave)="hideQuickActions()">
            <button nz-button nzType="text" class="quick-actions-trigger">
              <nz-icon nzType="plus-circle" nzTheme="outline"></nz-icon>
              <span>Actions rapides</span>
              <nz-icon [nzType]="showQuickActions ? 'up' : 'down'" nzTheme="outline"></nz-icon>
            </button>

            <div class="quick-actions-dropdown"
                 [class.show]="showQuickActions"
                 *ngIf="showQuickActions"
                 (mouseenter)="showQuickActionsMenu()"
                 (mouseleave)="hideQuickActions()">
              <div class="quick-actions-header">
                <h4>Actions rapides</h4>
                <p>Créer rapidement de nouveaux éléments</p>
              </div>

              <div class="quick-actions-grid">
                <div class="quick-action-item" (click)="navigateToNewMember()">
                  <div class="action-icon member-icon">
                    <nz-icon nzType="user-add" nzTheme="outline"></nz-icon>
                  </div>
                  <div class="action-content">
                    <span class="action-label">Nouveau membre</span>
                    <span class="action-description">Ajouter un membre</span>
                  </div>
                </div>

                <div class="quick-action-item" (click)="navigateToNewReservation()">
                  <div class="action-icon reservation-icon">
                    <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
                  </div>
                  <div class="action-content">
                    <span class="action-label">Nouvelle réservation</span>
                    <span class="action-description">Réserver un espace</span>
                  </div>
                </div>

                <div class="quick-action-item" (click)="navigateToNewSpace()">
                  <div class="action-icon space-icon">
                    <nz-icon nzType="home" nzTheme="outline"></nz-icon>
                  </div>
                  <div class="action-content">
                    <span class="action-label">Nouvel espace</span>
                    <span class="action-description">Créer un espace</span>
                  </div>
                </div>

                <div class="quick-action-item" (click)="navigateToNewInvoice()">
                  <div class="action-icon invoice-icon">
                    <nz-icon nzType="file-text" nzTheme="outline"></nz-icon>
                  </div>
                  <div class="action-content">
                    <span class="action-label">Nouvelle facture</span>
                    <span class="action-description">Générer une facture</span>
                  </div>
                </div>

                <div class="quick-action-item" (click)="navigateToNewSubscription()">
                  <div class="action-icon subscription-icon">
                    <nz-icon nzType="credit-card" nzTheme="outline"></nz-icon>
                  </div>
                  <div class="action-content">
                    <span class="action-label">Nouvel abonnement</span>
                    <span class="action-description">Créer un abonnement</span>
                  </div>
                </div>

                <div class="quick-action-item" (click)="navigateToSpaceCalendar()">
                  <div class="action-icon calendar-icon">
                    <nz-icon nzType="schedule" nzTheme="outline"></nz-icon>
                  </div>
                  <div class="action-content">
                    <span class="action-label">Planning espaces</span>
                    <span class="action-description">Voir le planning</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="header-right">
          <!-- Notifications -->
          <app-notifications></app-notifications>

          <div class="user-profile">
            <div class="user-info-container" nz-dropdown [nzDropdownMenu]="userMenu" nzPlacement="bottomRight">
                <nz-avatar
                  [nzSrc]="userAvatar"
                  [nzIcon]="'user'"
                  nzSize="default"
                  class="user-avatar-img"
                ></nz-avatar>
                <div class="user-info">
                  <span class="user-name">{{ managerName }}</span>
                  <span class="user-role">{{ currentUser?.role === 'admin' ? 'Administrateur' : 'Gestionnaire' }}</span>
                </div>
                <nz-icon nzType="down" nzTheme="outline" class="dropdown-icon"></nz-icon>
              </div>

            <nz-dropdown-menu #userMenu="nzDropdownMenu">
              <ul nz-menu>
                <li nz-menu-item (click)="navigateToProfile()">
                  <nz-icon nzType="user" nzTheme="outline"></nz-icon>
                  Mon profil
                </li>
                <li nz-menu-item (click)="navigateToSettings()">
                  <nz-icon nzType="setting" nzTheme="outline"></nz-icon>
                  Paramètres
                </li>
                <li nz-menu-divider></li>
                <li nz-menu-item (click)="onLogout()" class="logout-item">
                  <nz-icon nzType="logout" nzTheme="outline"></nz-icon>
                  Se déconnecter
                </li>
              </ul>
            </nz-dropdown-menu>
          </div>
        </div>
      </div>
    </nz-header>

    <!-- Panel de sites -->
    <app-sites-panel [sidebarCollapsed]="isCollapsed"></app-sites-panel>

    <nz-content>
      <div class="inner-content">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
  </nz-layout>
</nz-layout>
</ng-template>
