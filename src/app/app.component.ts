import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { RouterOutlet, RouterLink, Router, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, filter } from 'rxjs';

import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NotificationsComponent } from './components/notifications/notifications.component';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzModalModule } from 'ng-zorro-antd/modal';

import { AuthService } from './services/auth.service';
import { User } from './models/auth.model';
import { SitesPanelComponent } from './components/sites-panel/sites-panel.component';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    RouterOutlet,
    RouterLink,
    NzIconModule,
    NzLayoutModule,
    NzMenuModule,
    NzButtonModule,
    NzAvatarModule,
    NzDropDownModule,
    NzDividerModule,
    NzMessageModule,
    NzModalModule,
    NotificationsComponent,
    SitesPanelComponent
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private readonly SIDEBAR_STATE_KEY = 'workeem_sidebar_collapsed';

  isCollapsed = false;
  isAuthenticated = false;
  currentUser: User | null = null;
  showQuickActions = false;
  private hideTimeout: any;

  // Mobile responsive properties
  isMobile = false;
  sidebarOpen = false;

  constructor(
    private router: Router,
    private authService: AuthService,
    private message: NzMessageService,
    private modal: NzModalService
  ) {}

  ngOnInit() {
    // Restaurer l'état du sidebar depuis localStorage
    this.restoreSidebarState();

    // Détecter si on est sur mobile au démarrage
    this.checkIfMobile();

    // S'abonner aux changements d'état d'authentification
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(authState => {
        this.isAuthenticated = authState.isAuthenticated;
        this.currentUser = authState.user;
      });

    // Fermer le sidebar mobile lors des changements de route
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        if (this.isMobile && this.sidebarOpen) {
          this.sidebarOpen = false;
        }
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Listener pour détecter les changements de taille d'écran
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkIfMobile();
    // Fermer le sidebar mobile si on passe en mode desktop
    if (!this.isMobile && this.sidebarOpen) {
      this.sidebarOpen = false;
    }
  }

  // Getters pour les templates
  get logoSrc() {
    if (this.isCollapsed) {
      return 'assets/images/logo-min.png';
    }
    return 'assets/images/logo.png';
  }

  get tenantName(): string {
    return this.currentUser?.tenantName || 'Workeem';
  }

  get managerName(): string {
    if (this.currentUser) {
      return `${this.currentUser.firstName} ${this.currentUser.lastName}`;
    }
    return 'Utilisateur';
  }

  get userAvatar(): string {
    return this.currentUser?.avatar || 'assets/images/avatar-default.png';
  }

  // Méthodes utilitaires
  isReservationRoute(): boolean {
    const url = this.router.url;
    return url.includes('/reservations') || url.includes('/reservation-form');
  }

  isLoginRoute(): boolean {
    return this.router.url === '/login';
  }

  // Méthodes d'authentification
  onLogout() {
    this.modal.confirm({
      nzTitle: 'Confirmer la déconnexion',
      nzContent: 'Êtes-vous sûr de vouloir vous déconnecter ?',
      nzOkText: 'Se déconnecter',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzOnOk: () => {
        this.performLogout();
      }
    });
  }

  private performLogout() {
    this.authService.logout().subscribe({
      next: () => {
        this.message.success('Déconnexion réussie');
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion:', error);
        this.message.error('Erreur lors de la déconnexion');
      }
    });
  }

  // Méthodes de gestion du sidebar
  private restoreSidebarState() {
    const savedState = localStorage.getItem(this.SIDEBAR_STATE_KEY);
    if (savedState !== null) {
      this.isCollapsed = JSON.parse(savedState);
    }
  }

  private saveSidebarState() {
    localStorage.setItem(this.SIDEBAR_STATE_KEY, JSON.stringify(this.isCollapsed));
  }

  toggleSidebar() {
    if (this.isMobile) {
      // Sur mobile, on gère l'ouverture/fermeture du sidebar
      this.sidebarOpen = !this.sidebarOpen;
    } else {
      // Sur desktop, on gère le collapse/expand
      this.isCollapsed = !this.isCollapsed;
      this.saveSidebarState();
    }
  }

  onSidebarCollapsedChange(collapsed: boolean) {
    this.isCollapsed = collapsed;
    this.saveSidebarState();
  }

  // Méthodes pour la gestion mobile
  private checkIfMobile() {
    this.isMobile = window.innerWidth <= 768;
  }

  closeMobileSidebar() {
    if (this.isMobile) {
      this.sidebarOpen = false;
    }
  }

  // Méthode pour obtenir le titre de la page actuelle
  getPageTitle(): string {
    const url = this.router.url;
    const titleMap: { [key: string]: string } = {
      '/welcome': 'Dashboard',
      '/members': 'Membres',
      '/subscriptions': 'Abonnements',
      '/spaces': 'Espaces',
      '/reservations': 'Réservations',
      '/billing': 'Facturation',
      '/statistics': 'Stats'
    };

    // Chercher une correspondance exacte ou partielle
    for (const [route, title] of Object.entries(titleMap)) {
      if (url.startsWith(route)) {
        return title;
      }
    }

    return 'Workeem';
  }

  // Méthodes pour le menu de raccourcis
  hideQuickActions() {
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
    }
    this.hideTimeout = setTimeout(() => {
      this.showQuickActions = false;
    }, 300); // Délai de 300ms pour permettre le passage de la souris
  }

  showQuickActionsMenu() {
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
    }
    this.showQuickActions = true;
  }

  // Méthodes de navigation pour les actions rapides
  navigateToNewMember() {
    this.router.navigate(['/members/new']);
    this.showQuickActions = false;
  }

  navigateToNewReservation() {
    this.router.navigate(['/reservation-form']);
    this.showQuickActions = false;
  }

  navigateToNewSpace() {
    this.router.navigate(['/spaces/new']);
    this.showQuickActions = false;
  }

  navigateToNewInvoice() {
    this.router.navigate(['/billing']);
    this.showQuickActions = false;
    // TODO: Ouvrir directement le modal de nouvelle facture
  }

  navigateToNewSubscription() {
    this.router.navigate(['/subscriptions/new']);
    this.showQuickActions = false;
  }

  navigateToSpaceCalendar() {
    this.router.navigate(['/spaces/calendar']);
    this.showQuickActions = false;
  }

  // Méthodes de navigation
  navigateToProfile() {
    // TODO: Implémenter la navigation vers le profil utilisateur
    this.message.info('Fonctionnalité de profil à venir');
  }

  navigateToSettings() {
    // TODO: Implémenter la navigation vers les paramètres
    this.message.info('Fonctionnalité de paramètres à venir');
  }
}
