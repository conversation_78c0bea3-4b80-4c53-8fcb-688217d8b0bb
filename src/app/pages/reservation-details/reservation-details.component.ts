import { Component, OnInit } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzTimelineModule } from 'ng-zorro-antd/timeline';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzResultModule } from 'ng-zorro-antd/result';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageModule } from 'ng-zorro-antd/message';

import { Reservation } from '../../models/reservation.model';
import { Space } from '../../models/space.model';

@Component({
  selector: 'app-reservation-details',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzTagModule,
    NzAvatarModule,
    NzTimelineModule,
    NzSpinModule,
    NzResultModule,
    NzModalModule,
    NzMessageModule
  ],
  templateUrl: './reservation-details.component.html',
  styleUrl: './reservation-details.component.css'
})
export class ReservationDetailsComponent implements OnInit {
  reservation: Reservation | null = null;
  spaceInfo: Space | null = null;
  loading = false;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private message: NzMessageService,
    private modal: NzModalService
  ) {}

  ngOnInit() {
    const reservationId = this.route.snapshot.paramMap.get('id');
    if (reservationId) {
      this.loadReservation(reservationId);
    } else {
      this.error = 'ID de réservation manquant';
    }
  }

  private loadReservation(id: string) {
    this.loading = true;

    // Simulation de données de réservation avec toutes les réservations du calendrier
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    const dayAfter = new Date(today);
    dayAfter.setDate(today.getDate() + 2);
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);

    const mockReservations: Reservation[] = [
      // Aujourd'hui
      {
        id: '1',
        spaceId: '1',
        spaceName: 'Salle de réunion Alpha',
        userId: '1',
        userName: 'Houssam Benali',
        userEmail: '<EMAIL>',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 9, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 11, 0),
        purpose: 'Réunion équipe développement',
        status: 'confirmed',
        numberOfPeople: 6,
        notes: 'Prévoir projecteur et tableau blanc',
        recurrence: 'weekly',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '2',
        spaceId: '2',
        spaceName: 'Bureau privé B1',
        userId: '2',
        userName: 'Aicha El Fassi',
        userEmail: '<EMAIL>',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 14, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 16, 0),
        purpose: 'Entretien client important',
        status: 'confirmed',
        numberOfPeople: 2,
        notes: 'Client VIP - prévoir café et viennoiseries',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '3',
        spaceId: '3',
        spaceName: 'Espace coworking',
        userId: '3',
        userName: 'Omar Tazi',
        userEmail: '<EMAIL>',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 16, 30),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 18, 0),
        purpose: 'Session de brainstorming',
        status: 'confirmed',
        numberOfPeople: 4,
        notes: 'Équipe créative - besoin de tableaux blancs',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Demain
      {
        id: '4',
        spaceId: '1',
        spaceName: 'Salle de réunion Alpha',
        userId: '4',
        userName: 'Fatima Alaoui',
        userEmail: '<EMAIL>',
        startTime: new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 10, 0),
        endTime: new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 12, 0),
        purpose: 'Formation en management',
        status: 'pending',
        numberOfPeople: 8,
        notes: 'Formation pour managers - prévoir supports papier',
        recurrence: 'monthly',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '5',
        spaceId: '4',
        spaceName: 'Salle de conférence',
        userId: '5',
        userName: 'Mehdi Benjelloun',
        userEmail: '<EMAIL>',
        startTime: new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 15, 0),
        endTime: new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 17, 0),
        purpose: 'Présentation projet client',
        status: 'confirmed',
        numberOfPeople: 12,
        notes: 'Présentation importante - vérifier équipement audiovisuel',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Après-demain
      {
        id: '6',
        spaceId: '2',
        spaceName: 'Bureau privé B1',
        userId: '6',
        userName: 'Salma Chraibi',
        userEmail: '<EMAIL>',
        startTime: new Date(dayAfter.getFullYear(), dayAfter.getMonth(), dayAfter.getDate(), 9, 0),
        endTime: new Date(dayAfter.getFullYear(), dayAfter.getMonth(), dayAfter.getDate(), 12, 0),
        purpose: 'Travail sur projet de fin d\'études',
        status: 'confirmed',
        numberOfPeople: 1,
        notes: 'Étudiante - accès WiFi et prises électriques nécessaires',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '7',
        spaceId: '3',
        spaceName: 'Espace coworking',
        userId: '7',
        userName: 'Karim Ouali',
        userEmail: '<EMAIL>',
        startTime: new Date(dayAfter.getFullYear(), dayAfter.getMonth(), dayAfter.getDate(), 14, 0),
        endTime: new Date(dayAfter.getFullYear(), dayAfter.getMonth(), dayAfter.getDate(), 18, 0),
        purpose: 'Conception plans architecturaux',
        status: 'confirmed',
        numberOfPeople: 3,
        notes: 'Travail sur plans - besoin de grandes tables',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Semaine prochaine
      {
        id: '8',
        spaceId: '1',
        spaceName: 'Salle de réunion Alpha',
        userId: '8',
        userName: 'Nadia Bennani',
        userEmail: '<EMAIL>',
        startTime: new Date(nextWeek.getFullYear(), nextWeek.getMonth(), nextWeek.getDate(), 11, 0),
        endTime: new Date(nextWeek.getFullYear(), nextWeek.getMonth(), nextWeek.getDate(), 13, 0),
        purpose: 'Consultation juridique',
        status: 'pending',
        numberOfPeople: 3,
        notes: 'Consultation confidentielle - assurer la discrétion',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '9',
        spaceId: '4',
        spaceName: 'Salle de conférence',
        userId: '1',
        userName: 'Houssam Benali',
        userEmail: '<EMAIL>',
        startTime: new Date(nextWeek.getFullYear(), nextWeek.getMonth(), nextWeek.getDate(), 15, 30),
        endTime: new Date(nextWeek.getFullYear(), nextWeek.getMonth(), nextWeek.getDate(), 17, 30),
        purpose: 'Demo produit pour investisseurs',
        status: 'confirmed',
        numberOfPeople: 15,
        notes: 'Présentation cruciale - double vérification équipement',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Réservations récurrentes
      {
        id: '10',
        spaceId: '3',
        spaceName: 'Espace coworking',
        userId: '3',
        userName: 'Omar Tazi',
        userEmail: '<EMAIL>',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 3, 8, 30),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 3, 12, 0),
        purpose: 'Développement startup - Sprint quotidien',
        status: 'confirmed',
        numberOfPeople: 5,
        notes: 'Sprint quotidien - équipe développement',
        recurrence: 'daily',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '11',
        spaceId: '2',
        spaceName: 'Bureau privé B1',
        userId: '4',
        userName: 'Fatima Alaoui',
        userEmail: '<EMAIL>',
        startTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 4, 13, 0),
        endTime: new Date(today.getFullYear(), today.getMonth(), today.getDate() + 4, 16, 0),
        purpose: 'Coaching d\'équipe',
        status: 'confirmed',
        numberOfPeople: 6,
        notes: 'Session de coaching - ambiance détendue souhaitée',
        recurrence: 'weekly',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    const mockSpaces: Space[] = [
      {
        id: '1',
        name: 'Salle de réunion Alpha',
        type: 'meeting_room' as any,
        capacity: 12,
        location: 'Étage 1',
        floor: '1er',
        area: 25,
        status: 'available' as any,
        description: 'Salle de réunion moderne avec équipements audiovisuels',
        images: ['https://via.placeholder.com/400x300/6E56CF/FFFFFF?text=Salle+Alpha'],
        equipment: [],
        amenities: ['Projecteur HD', 'Tableau blanc', 'Système audio', 'WiFi haut débit', 'Climatisation'],
        availability: {} as any,
        pricing: {
          hourlyRate: 450,
          dailyRate: 3200,
          weeklyRate: 18000,
          monthlyRate: 60000,
          currency: 'MAD ',
          discounts: []
        },
        rules: ['Réservation obligatoire', 'Capacité maximale respectée'],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '2',
        name: 'Bureau privé B1',
        type: 'private_office' as any,
        capacity: 4,
        location: 'Étage 1',
        floor: '1er',
        area: 15,
        status: 'available' as any,
        description: 'Bureau privé pour travail concentré',
        images: ['https://via.placeholder.com/400x300/2ECC71/FFFFFF?text=Bureau+B1'],
        equipment: [],
        amenities: ['WiFi haut débit', 'Prises électriques', 'Éclairage naturel', 'Climatisation'],
        availability: {} as any,
        pricing: {
          hourlyRate: 250,
          dailyRate: 1800,
          weeklyRate: 10000,
          monthlyRate: 35000,
          currency: 'MAD ',
          discounts: []
        },
        rules: ['Réservation obligatoire', 'Silence requis'],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '3',
        name: 'Espace coworking',
        type: 'collaborative' as any,
        capacity: 20,
        location: 'Rez-de-chaussée',
        floor: 'RDC',
        area: 80,
        status: 'available' as any,
        description: 'Espace ouvert pour le travail collaboratif',
        images: ['https://via.placeholder.com/400x300/3498DB/FFFFFF?text=Coworking'],
        equipment: [],
        amenities: ['WiFi haut débit', 'Tableaux blancs', 'Mobilier flexible', 'Machine à café', 'Imprimante'],
        availability: {} as any,
        pricing: {
          hourlyRate: 0,
          dailyRate: 0,
          weeklyRate: 0,
          monthlyRate: 0,
          currency: 'MAD ',
          discounts: []
        },
        rules: ['Accès libre', 'Respect du silence relatif'],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '4',
        name: 'Salle de conférence',
        type: 'conference_room' as any,
        capacity: 50,
        location: 'Étage 2',
        floor: '2ème',
        area: 60,
        status: 'available' as any,
        description: 'Grande salle pour conférences et événements',
        images: ['https://via.placeholder.com/400x300/8E44AD/FFFFFF?text=Conference'],
        equipment: [],
        amenities: ['Projecteur 4K', 'Système audio professionnel', 'Éclairage modulable', 'Caméras de diffusion', 'Climatisation'],
        availability: {} as any,
        pricing: {
          hourlyRate: 800,
          dailyRate: 5000,
          weeklyRate: 30000,
          monthlyRate: 100000,
          currency: 'MAD ',
          discounts: []
        },
        rules: ['Réservation obligatoire', 'Événements autorisés'],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    setTimeout(() => {
      this.reservation = mockReservations.find(r => r.id === id) || null;
      if (this.reservation) {
        this.spaceInfo = mockSpaces.find(s => s.id === this.reservation!.spaceId) || null;
      }

      if (!this.reservation) {
        this.error = 'Réservation non trouvée';
      }
      this.loading = false;
    }, 500);
  }

  goBack() {
    this.location.back();
  }

  navigateToClient() {
    if (this.reservation) {
      this.router.navigate(['/members', this.reservation.userId]);
    }
  }

  editReservation() {
    if (this.reservation) {
      this.router.navigate(['/reservation-form'], {
        queryParams: { id: this.reservation.id }
      });
    }
  }

  confirmReservation() {
    if (!this.reservation || this.reservation.status === 'confirmed') return;

    this.modal.confirm({
      nzTitle: 'Confirmer la réservation',
      nzContent: 'Voulez-vous confirmer cette réservation ?',
      nzOkText: 'Confirmer',
      nzCancelText: 'Annuler',
      nzCentered: true,
      nzOnOk: () => {
        if (this.reservation) {
          this.reservation.status = 'confirmed';
          this.reservation.updatedAt = new Date();
          this.message.success('Réservation confirmée');
        }
      }
    });
  }

  duplicateReservation() {
    if (this.reservation) {
      this.router.navigate(['/reservation-form'], {
        queryParams: {
          duplicate: this.reservation.id,
          spaceId: this.reservation.spaceId,
          purpose: this.reservation.purpose
        }
      });
    }
  }

  sendNotification() {
    this.message.success('Notification envoyée au client');
  }

  cancelReservation() {
    this.modal.confirm({
      nzTitle: 'Annuler la réservation',
      nzContent: 'Voulez-vous vraiment annuler cette réservation ?<br><br>Cette action est irréversible.',
      nzOkText: 'Annuler la réservation',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Retour',
      nzCentered: true,
      nzOnOk: () => {
        if (this.reservation) {
          this.reservation.status = 'cancelled';
          this.message.success('Réservation annulée');
          this.goBack();
        }
      }
    });
  }

  getDuration(): string {
    if (!this.reservation) return '';

    const start = new Date(this.reservation.startTime);
    const end = new Date(this.reservation.endTime);
    const diffMs = end.getTime() - start.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    let duration = '';

    if (diffDays > 0) {
      duration += `${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    }

    if (diffHours > 0) {
      if (duration) duration += ', ';
      duration += `${diffHours}h`;
    }

    if (diffMinutes > 0 && diffDays === 0) {
      if (duration) duration += '';
      duration += `${diffMinutes}min`;
    }

    return duration || '0min';
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'pending': return 'orange';
      case 'confirmed': return 'green';
      case 'cancelled': return 'red';
      case 'completed': return 'blue';
      default: return 'default';
    }
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'pending': return 'En attente';
      case 'confirmed': return 'Confirmée';
      case 'cancelled': return 'Annulée';
      case 'completed': return 'Terminée';
      default: return status;
    }
  }

  getRecurrenceLabel(recurrence: string): string {
    switch (recurrence) {
      case 'daily': return 'Quotidienne';
      case 'weekly': return 'Hebdomadaire';
      case 'monthly': return 'Mensuelle';
      case 'none': return 'Aucune';
      default: return recurrence;
    }
  }

  getClientInitials(): string {
    if (!this.reservation) return 'U';
    const names = this.reservation.userName.split(' ');
    return names.map(name => name.charAt(0)).join('').toUpperCase();
  }

  getFormattedDate(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';

      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  }

  getFormattedDateTime(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';

      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  }

  getFormattedFullDate(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';

      return dateObj.toLocaleDateString('fr-FR', {
        weekday: 'long',
        day: '2-digit',
        month: 'long',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  }
}
