/* Container principal */
.member-form-container {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
}

/* En-tête de page */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .anticon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  line-height: 1.4;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.cancel-button {
  height: 40px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  padding: 0 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  border: 1px solid #D1D1D6 !important;
  color: #8E8E93 !important;
  background-color: #FFFFFF !important;
}

.cancel-button:hover {
  border-color: #6E56CF !important;
  color: #6E56CF !important;
  background-color: #F8F9FA !important;
}

/* Card */
.form-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #E5E5EA;
}

.form-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA;
  padding: 16px 24px;
}

.form-card .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
  color: #1C1C1E;
}

.form-card .ant-card-body {
  padding: 24px;
}

/* Sections de formulaire */
.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #F0F0F0;
}

.form-section:last-of-type {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.form-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #6E56CF;
  display: inline-block;
}

/* Éléments de formulaire */
.ant-form-item {
  margin-bottom: 20px;
}

.ant-form-item-label > label {
  font-size: 14px;
  font-weight: 500;
  color: #1C1C1E;
}

.ant-input,
.ant-select-selector,
.ant-picker {
  border-radius: 8px;
  border: 1px solid #D1D1D6;
  transition: all 0.2s ease;
  font-size: 14px;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector,
.ant-picker:focus {
  border-color: #6E56CF;
  box-shadow: 0 0 0 2px rgba(110, 86, 207, 0.1);
}

.ant-input.error,
.ant-select.error .ant-select-selector {
  border-color: #FF3B30;
}

.ant-input.error:focus,
.ant-select.error.ant-select-focused .ant-select-selector {
  border-color: #FF3B30;
  box-shadow: 0 0 0 2px rgba(255, 59, 48, 0.1);
}

/* Textarea */
textarea.ant-input {
  resize: vertical;
  min-height: 80px;
}

/* Date picker */
.ant-picker {
  width: 100%;
}

/* Actions du formulaire */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #E5E5EA;
}

.cancel-action {
  height: 40px !important;
  border-radius: 8px !important;
  padding: 0 20px !important;
  font-weight: 500 !important;
  border: 1px solid #D1D1D6 !important;
  color: #8E8E93 !important;
  background-color: #FFFFFF !important;
}

.cancel-action:hover {
  border-color: #6E56CF !important;
  color: #6E56CF !important;
  background-color: #F8F9FA !important;
}

.submit-action {
  height: 40px !important;
  border-radius: 8px !important;
  padding: 0 24px !important;
  font-weight: 500 !important;
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
}

.submit-action:hover {
  background-color: #5A47B8 !important;
  border-color: #5A47B8 !important;
}

.submit-action:disabled {
  background-color: #D1D1D6 !important;
  border-color: #D1D1D6 !important;
  color: #8E8E93 !important;
}

/* Messages d'erreur */
.ant-form-item-explain-error {
  font-size: 12px;
  color: #FF3B30;
  margin-top: 4px;
}

/* Responsive */
@media (max-width: 768px) {
  .member-form-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .cancel-action,
  .submit-action {
    width: 100%;
    justify-content: center !important;
  }

  .page-title {
    font-size: 24px;
  }

  .page-title .anticon {
    font-size: 28px;
  }

  .form-section h3 {
    font-size: 16px;
  }

  /* Colonnes en mobile */
  .ant-col {
    margin-bottom: 0;
  }

  [nz-col] {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* États de chargement */
.ant-spin-container {
  min-height: 200px;
}

/* Focus visible pour l'accessibilité */
.ant-input:focus-visible,
.ant-select-selector:focus-visible,
.ant-picker:focus-visible {
  outline: 2px solid #6E56CF;
  outline-offset: 2px;
}

/* Amélioration des selects */
.ant-select-dropdown {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #E5E5EA;
}

.ant-select-item-option-selected {
  background-color: #EDE9F8;
  color: #6E56CF;
}

.ant-select-item-option:hover {
  background-color: #F8F9FA;
}

/* Animation des transitions */
.ant-input,
.ant-select-selector,
.ant-picker,
.cancel-button,
.cancel-action,
.submit-action {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Amélioration de la lisibilité */
.ant-form-item-label {
  line-height: 1.4;
}

.ant-input::placeholder,
.ant-select-selection-placeholder {
  color: #8E8E93;
  font-style: italic;
}
