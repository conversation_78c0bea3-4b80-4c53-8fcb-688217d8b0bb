/* Container principal */
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  gap: 32px;
}

/* Background avec gradient violet soft */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #E8E2FF 0%, #F3F0FF 50%, #FDFCFF 100%);
  z-index: 1;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #F8F6FF 0%, #F0EBFF 30%, #E8E2FF 70%, #E0D9FF 100%);
  opacity: 1;
}

/* Section logo en dehors de la card */
.logo-section {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  margin-bottom: 0;
}

.logo-section .logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-section .logo {
  width: 180px;
  height: auto;
  object-fit: contain;
  filter: drop-shadow(0 8px 24px rgba(110, 86, 207, 0.2));
}

/* Contenu principal */
.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 420px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(110, 86, 207, 0.15);
  border: 1px solid rgba(110, 86, 207, 0.1);
  margin: 0 20px;
}

/* Header simplifié */
.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-subtitle {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

/* Alertes */
.session-alert,
.error-alert {
  margin-bottom: 24px;
  border-radius: 12px !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Formulaire */
.login-form-container {
  margin-bottom: 32px;
}

.login-form .ant-form-item {
  margin-bottom: 20px;
}

.login-form .ant-form-item:last-child {
  margin-bottom: 0;
}

/* Champs de saisie */
.login-form .ant-input-affix-wrapper {
  border-radius: 12px !important;
  border: 2px solid #E5E5EA !important;
  background: #FAFAFA !important;
  transition: all 0.3s ease !important;
  height: 52px !important;
  font-size: 16px !important;
}

.login-form .ant-input-affix-wrapper:hover {
  border-color: #6E56CF !important;
  background: #FFFFFF !important;
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused {
  border-color: #6E56CF !important;
  background: #FFFFFF !important;
  box-shadow: 0 0 0 4px rgba(110, 86, 207, 0.1) !important;
}

.login-form .ant-input {
  background: transparent !important;
  border: none !important;
  font-size: 16px !important;
  color: #1C1C1E !important;
}

.login-form .ant-input::placeholder {
  color: #8E8E93 !important;
  font-weight: 400 !important;
}

/* Erreurs de validation */
.error-input {
  border-color: #FF3B30 !important;
  background: #FFF5F5 !important;
}

.error-input:hover,
.error-input:focus {
  border-color: #FF3B30 !important;
  box-shadow: 0 0 0 4px rgba(255, 59, 48, 0.1) !important;
}

/* Icônes des champs */
.login-form .ant-input-prefix {
  color: #8E8E93 !important;
  margin-right: 12px !important;
}

.password-toggle {
  cursor: pointer;
  color: #8E8E93 !important;
  transition: color 0.3s ease;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

.password-toggle:hover {
  color: #6E56CF !important;
}

/* Checkbox "Se souvenir de moi" */
.remember-me-item {
  margin-bottom: 24px !important;
}

.remember-me-checkbox {
  font-size: 14px !important;
  color: #1C1C1E !important;
}

.remember-me-checkbox .ant-checkbox-wrapper:hover .ant-checkbox-inner,
.remember-me-checkbox .ant-checkbox:hover .ant-checkbox-inner,
.remember-me-checkbox .ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: #6E56CF !important;
}

.remember-me-checkbox .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
}

/* Bouton de connexion */
.login-button-item {
  margin-bottom: 0 !important;
}

.login-button {
  height: 52px !important;
  border-radius: 12px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #6E56CF 0%, #8B7ED8 100%) !important;
  border: none !important;
  box-shadow: 0 8px 24px rgba(110, 86, 207, 0.3) !important;
  transition: all 0.3s ease !important;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 32px rgba(110, 86, 207, 0.4) !important;
  background: linear-gradient(135deg, #5A47B8 0%, #7B6BC7 100%) !important;
}

.login-button:active:not(:disabled) {
  transform: translateY(0) !important;
  box-shadow: 0 6px 20px rgba(110, 86, 207, 0.3) !important;
}

.login-button:disabled {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
  transform: none !important;
}



/* Footer */
.login-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #E5E5EA;
}

.login-footer p {
  font-size: 12px;
  color: #8E8E93;
  margin: 4px 0;
  line-height: 1.4;
}

/* Messages d'erreur personnalisés */
.ant-form-item-explain-error {
  font-size: 13px !important;
  color: #FF3B30 !important;
  margin-top: 6px !important;
}

/* Responsive design */
@media (max-width: 480px) {
  .login-container {
    gap: 24px;
    padding: 20px 0;
  }

  .logo-section .logo {
    width: 140px;
  }

  .login-content {
    margin: 0 10px;
    padding: 24px;
    border-radius: 20px;
  }

  .login-subtitle {
    font-size: 14px;
  }

  .login-form .ant-input-affix-wrapper {
    height: 48px !important;
    font-size: 15px !important;
  }

  .login-button {
    height: 48px !important;
    font-size: 15px !important;
  }
}

/* Animation d'entrée */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-content {
  animation: fadeInUp 0.6s ease-out;
}

/* Focus visible pour l'accessibilité */
.login-button:focus-visible {
  outline: 2px solid #6E56CF;
  outline-offset: 2px;
}

/* Amélioration du contraste pour l'accessibilité */
@media (prefers-contrast: high) {
  .login-content {
    background: rgba(255, 255, 255, 1);
    border: 2px solid #000000;
  }

  .tenant-name {
    color: #000000;
  }

  .login-subtitle {
    color: #666666;
  }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  .login-content {
    background: rgba(28, 28, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .tenant-name {
    color: #FFFFFF;
  }

  .login-subtitle {
    color: #8E8E93;
  }

  .login-form .ant-input-affix-wrapper {
    background: #2C2C2E !important;
    border-color: #3A3A3C !important;
  }

  .login-form .ant-input {
    color: #FFFFFF !important;
  }

  .test-credentials {
    background: #2C2C2E;
    border-color: #3A3A3C;
  }

  .test-info p {
    color: #FFFFFF;
  }

  .login-footer {
    border-top-color: #3A3A3C;
  }
}