import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { BillingService } from '../../services/billing.service';
import { MemberService } from '../../services/member.service';
import { Invoice, InvoiceItem } from '../../models/invoice.model';
import { Member } from '../../models/member.model';

@Component({
  selector: 'app-billing-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzCardModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzSelectModule,
    NzDatePickerModule,
    NzInputNumberModule,
    NzIconModule,
    NzTableModule,
    NzDividerModule
  ],
  templateUrl: './billing-form.component.html',
  styleUrls: ['./billing-form.component.css']
})
export class BillingFormComponent implements OnInit {
  billingForm!: FormGroup;
  members: Member[] = [];
  isEditMode = false;
  invoiceId: string | null = null;
  loading = false;
  submitting = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private billingService: BillingService,
    private memberService: MemberService,
    private message: NzMessageService
  ) {}

  ngOnInit() {
    this.initForm();
    this.loadMembers();
    
    // Vérifier si on est en mode édition
    this.invoiceId = this.route.snapshot.paramMap.get('id');
    if (this.invoiceId) {
      this.isEditMode = true;
      this.loadInvoice(this.invoiceId);
    } else {
      this.addItem(); // Ajouter un item par défaut
    }
  }

  initForm() {
    this.billingForm = this.fb.group({
      memberId: ['', [Validators.required]],
      issueDate: [new Date(), [Validators.required]],
      dueDate: [new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), [Validators.required]],
      notes: [''],
      items: this.fb.array([])
    });
  }

  get items() {
    return this.billingForm.get('items') as FormArray;
  }

  createItemForm(): FormGroup {
    return this.fb.group({
      description: ['', [Validators.required]],
      quantity: [1, [Validators.required, Validators.min(1)]],
      unitPrice: [0, [Validators.required, Validators.min(0)]],
      type: ['service', [Validators.required]]
    });
  }

  addItem() {
    this.items.push(this.createItemForm());
  }

  removeItem(index: number) {
    if (this.items.length > 1) {
      this.items.removeAt(index);
    }
  }

  getItemTotal(index: number): number {
    const item = this.items.at(index);
    const quantity = item.get('quantity')?.value || 0;
    const unitPrice = item.get('unitPrice')?.value || 0;
    return quantity * unitPrice;
  }

  getSubtotal(): number {
    let subtotal = 0;
    for (let i = 0; i < this.items.length; i++) {
      subtotal += this.getItemTotal(i);
    }
    return subtotal;
  }

  getTaxAmount(): number {
    return this.getSubtotal() * 0.2; // 20% TVA
  }

  getTotal(): number {
    return this.getSubtotal() + this.getTaxAmount();
  }

  loadMembers() {
    this.memberService.getMembers().subscribe({
      next: (members) => {
        this.members = members;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des membres:', error);
        this.message.error('Erreur lors du chargement des membres');
      }
    });
  }

  loadInvoice(id: string) {
    this.loading = true;
    this.billingService.getInvoice(id).subscribe({
      next: (invoice) => {
        this.populateForm(invoice);
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement de la facture:', error);
        this.message.error('Erreur lors du chargement de la facture');
        this.loading = false;
      }
    });
  }

  populateForm(invoice: Invoice) {
    this.billingForm.patchValue({
      memberId: invoice.memberId,
      issueDate: invoice.issueDate,
      dueDate: invoice.dueDate,
      notes: invoice.notes
    });

    // Vider les items existants
    while (this.items.length !== 0) {
      this.items.removeAt(0);
    }

    // Ajouter les items de la facture
    invoice.items.forEach(item => {
      const itemForm = this.createItemForm();
      itemForm.patchValue({
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        type: item.type
      });
      this.items.push(itemForm);
    });
  }

  onSubmit() {
    if (this.billingForm.valid) {
      this.submitting = true;
      const formValue = this.billingForm.value;
      
      const invoiceItems: InvoiceItem[] = formValue.items.map((item: any) => ({
        id: this.generateId(),
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice,
        type: item.type
      }));

      if (this.isEditMode && this.invoiceId) {
        // Mode édition
        this.billingService.updateInvoice(this.invoiceId, {
          ...formValue,
          items: invoiceItems
        }).subscribe({
          next: () => {
            this.message.success('Facture mise à jour avec succès');
            this.router.navigate(['/billing']);
          },
          error: (error) => {
            console.error('Erreur lors de la mise à jour:', error);
            this.message.error('Erreur lors de la mise à jour');
            this.submitting = false;
          }
        });
      } else {
        // Mode création
        this.billingService.generateInvoice(formValue.memberId, invoiceItems).subscribe({
          next: () => {
            this.message.success('Facture créée avec succès');
            this.router.navigate(['/billing']);
          },
          error: (error) => {
            console.error('Erreur lors de la création:', error);
            this.message.error('Erreur lors de la création');
            this.submitting = false;
          }
        });
      }
    } else {
      this.message.error('Veuillez remplir tous les champs obligatoires');
    }
  }

  onCancel() {
    this.router.navigate(['/billing']);
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
