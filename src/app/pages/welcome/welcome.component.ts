import { Component, OnInit, signal } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { forkJoin } from 'rxjs';

import { MemberService } from '../../services/member.service';
import { BillingService } from '../../services/billing.service';
import { SpaceService } from '../../services/space.service';
import { SubscriptionService } from '../../services/subscription.service';
import { Member } from '../../models/member.model';
import { DailyCalendarComponent } from '../../components/daily-calendar/daily-calendar.component';

interface DashboardStats {
  totalMembers: number;
  activeMembers: number;
  totalSpaces: number;
  availableSpaces: number;
  totalReservations: number;
  activeReservations: number;
  monthlyRevenue: number;
  totalRevenue: number;
  pendingPayments: number;
  overduePayments: number;
  activeSubscriptions: number;
  expiringSubscriptions: number;
}

@Component({
  selector: 'app-welcome',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzSpinModule,
    DailyCalendarComponent
  ],
  templateUrl: './welcome.component.html',
  styleUrl: './welcome.component.css'
})
export class WelcomeComponent implements OnInit {
  // Signaux pour la gestion d'état
  private statsSignal = signal<DashboardStats>({
    totalMembers: 0,
    activeMembers: 0,
    totalSpaces: 0,
    availableSpaces: 0,
    totalReservations: 0,
    activeReservations: 0,
    monthlyRevenue: 0,
    totalRevenue: 0,
    pendingPayments: 0,
    overduePayments: 0,
    activeSubscriptions: 0,
    expiringSubscriptions: 0
  });
  private loadingSignal = signal<boolean>(false);

  // Getters pour les templates
  get stats() { return this.statsSignal(); }
  get loading() { return this.loadingSignal(); }

  // Données pour les calculs
  private allMembers: Member[] = [];
  private allSpaces: any[] = [];

  constructor(
    private router: Router,
    private memberService: MemberService,
    private billingService: BillingService,
    private spaceService: SpaceService,
    private subscriptionService: SubscriptionService
  ) {}

  ngOnInit() {
    this.loadDashboardStats();
  }

  private loadDashboardStats() {
    this.loadingSignal.set(true);
    console.log('Starting to load dashboard stats...');

    // Utiliser des données mock temporaires pour tester
    setTimeout(() => {
      try {
        // Données mock pour tester
        const mockStats: DashboardStats = {
          totalMembers: 8,
          activeMembers: 6,
          totalSpaces: 5,
          availableSpaces: 3,
          totalReservations: 11,
          activeReservations: 8,
          monthlyRevenue: 10584,
          totalRevenue: 45230,
          pendingPayments: 2340,
          overduePayments: 890,
          activeSubscriptions: 6,
          expiringSubscriptions: 2
        };

        this.allMembers = []; // Mock vide pour l'instant
        this.allSpaces = []; // Mock vide pour l'instant

        console.log('Mock stats loaded:', mockStats);
        this.statsSignal.set(mockStats);
        this.loadingSignal.set(false);
      } catch (error) {
        console.error('Erreur lors du chargement des statistiques mock:', error);
        this.loadingSignal.set(false);
      }
    }, 1000);
  }

  // Méthodes pour les calculs des statistiques supplémentaires
  getOccupancyRate(): number {
    const stats = this.statsSignal();
    if (stats.totalSpaces === 0) return 0;
    const occupiedSpaces = stats.totalSpaces - stats.availableSpaces;
    return Math.round((occupiedSpaces / stats.totalSpaces) * 100);
  }

  getNewMembersThisMonth(): number {
    // Pour l'instant, retourner une valeur mock
    return 3;
  }

  // Méthodes de navigation pour les actions rapides
  navigateToNewMember(): void {
    this.router.navigate(['/members/new']);
  }

  navigateToNewReservation(): void {
    this.router.navigate(['/reservation-form']);
  }

  navigateToNewSpace(): void {
    this.router.navigate(['/spaces/new']);
  }

  navigateToNewBilling(): void {
    this.router.navigate(['/billing/new']);
  }
}
