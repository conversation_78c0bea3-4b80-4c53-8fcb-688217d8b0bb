package com.workeem.workeem_api.tenant.service;

import com.workeem.workeem_api.tenant.dto.CreateSiteRequestDto;
import com.workeem.workeem_api.tenant.dto.SiteResponseDto;
import com.workeem.workeem_api.tenant.dto.UpdateSiteRequestDto;
import com.workeem.workeem_api.tenant.entity.Site;
import com.workeem.workeem_api.tenant.exception.SiteAlreadyExistsException;
import com.workeem.workeem_api.tenant.exception.SiteNotFoundException;
import com.workeem.workeem_api.tenant.repository.SiteRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SiteServiceImplTest {

    @Mock
    private SiteRepository siteRepository;

    @InjectMocks
    private SiteServiceImpl siteService;

    private CreateSiteRequestDto createSiteRequestDto;
    private Site site;
    private SiteResponseDto expectedResponse;

    @BeforeEach
    void setUp() {
        createSiteRequestDto = CreateSiteRequestDto.builder()
                .name("Test Site")
                .city("Test City")
                .country("Test Country")
                .address("123 Test Street")
                .isActive(true)
                .spacesCount(10)
                .membersCount(50)
                .build();

        site = Site.builder()
                .id(1L)
                .name("Test Site")
                .city("Test City")
                .country("Test Country")
                .address("123 Test Street")
                .isActive(true)
                .spacesCount(10)
                .membersCount(50)
                .createdDate(LocalDateTime.now())
                .createdBy("admin")
                .build();

        expectedResponse = SiteResponseDto.builder()
                .id(1L)
                .name("Test Site")
                .city("Test City")
                .country("Test Country")
                .address("123 Test Street")
                .isActive(true)
                .spacesCount(10)
                .membersCount(50)
                .createdDate(site.getCreatedDate())
                .createdBy("admin")
                .build();
    }

    @Test
    void createSite_ShouldReturnSiteResponseDto_WhenValidRequest() {
        // Given
        when(siteRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
        when(siteRepository.save(any(Site.class))).thenReturn(site);

        // When
        SiteResponseDto result = siteService.createSite(createSiteRequestDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("Test Site");
        assertThat(result.getCity()).isEqualTo("Test City");
        assertThat(result.getCountry()).isEqualTo("Test Country");
        assertThat(result.getAddress()).isEqualTo("123 Test Street");
        assertThat(result.getIsActive()).isTrue();
        assertThat(result.getSpacesCount()).isEqualTo(10);
        assertThat(result.getMembersCount()).isEqualTo(50);

        verify(siteRepository).existsByNameIgnoreCase("Test Site");
        verify(siteRepository).save(any(Site.class));
    }

    @Test
    void createSite_ShouldThrowSiteAlreadyExistsException_WhenSiteNameExists() {
        // Given
        when(siteRepository.existsByNameIgnoreCase(anyString())).thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> siteService.createSite(createSiteRequestDto))
                .isInstanceOf(SiteAlreadyExistsException.class)
                .hasMessage("Site with name 'Test Site' already exists");

        verify(siteRepository).existsByNameIgnoreCase("Test Site");
        verify(siteRepository, never()).save(any(Site.class));
    }

    @Test
    void getAllSites_ShouldReturnListOfSites() {
        // Given
        List<Site> sites = Arrays.asList(site);
        when(siteRepository.findAllByOrderByCreatedDateDesc()).thenReturn(sites);

        // When
        List<SiteResponseDto> result = siteService.getAllSites();

        // Then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getName()).isEqualTo("Test Site");
        verify(siteRepository).findAllByOrderByCreatedDateDesc();
    }

    @Test
    void getSiteById_ShouldReturnSite_WhenSiteExists() {
        // Given
        when(siteRepository.findById(1L)).thenReturn(Optional.of(site));

        // When
        SiteResponseDto result = siteService.getSiteById(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getName()).isEqualTo("Test Site");
        verify(siteRepository).findById(1L);
    }

    @Test
    void getSiteById_ShouldThrowSiteNotFoundException_WhenSiteDoesNotExist() {
        // Given
        when(siteRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> siteService.getSiteById(1L))
                .isInstanceOf(SiteNotFoundException.class)
                .hasMessage("Site with ID 1 not found");

        verify(siteRepository).findById(1L);
    }

    @Test
    void updateSite_ShouldReturnUpdatedSite_WhenValidRequest() {
        // Given
        UpdateSiteRequestDto updateRequest = UpdateSiteRequestDto.builder()
                .name("Updated Site")
                .city("Updated City")
                .build();

        Site updatedSite = Site.builder()
                .id(1L)
                .name("Updated Site")
                .city("Updated City")
                .country("Test Country")
                .address("123 Test Street")
                .isActive(true)
                .spacesCount(10)
                .membersCount(50)
                .build();

        when(siteRepository.findById(1L)).thenReturn(Optional.of(site));
        when(siteRepository.existsByNameIgnoreCase("Updated Site")).thenReturn(false);
        when(siteRepository.save(any(Site.class))).thenReturn(updatedSite);

        // When
        SiteResponseDto result = siteService.updateSite(1L, updateRequest);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo("Updated Site");
        assertThat(result.getCity()).isEqualTo("Updated City");
        verify(siteRepository).findById(1L);
        verify(siteRepository).save(any(Site.class));
    }

    @Test
    void deleteSite_ShouldDeleteSite_WhenSiteExists() {
        // Given
        when(siteRepository.existsById(1L)).thenReturn(true);

        // When
        siteService.deleteSite(1L);

        // Then
        verify(siteRepository).existsById(1L);
        verify(siteRepository).deleteById(1L);
    }

    @Test
    void deleteSite_ShouldThrowSiteNotFoundException_WhenSiteDoesNotExist() {
        // Given
        when(siteRepository.existsById(1L)).thenReturn(false);

        // When & Then
        assertThatThrownBy(() -> siteService.deleteSite(1L))
                .isInstanceOf(SiteNotFoundException.class)
                .hasMessage("Site with ID 1 not found");

        verify(siteRepository).existsById(1L);
        verify(siteRepository, never()).deleteById(1L);
    }

    @Test
    void toggleSiteStatus_ShouldToggleStatus_WhenSiteExists() {
        // Given
        Site toggledSite = Site.builder()
                .id(1L)
                .name("Test Site")
                .city("Test City")
                .country("Test Country")
                .address("123 Test Street")
                .isActive(false) // toggled from true to false
                .spacesCount(10)
                .membersCount(50)
                .build();

        when(siteRepository.findById(1L)).thenReturn(Optional.of(site));
        when(siteRepository.save(any(Site.class))).thenReturn(toggledSite);

        // When
        SiteResponseDto result = siteService.toggleSiteStatus(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getIsActive()).isFalse();
        verify(siteRepository).findById(1L);
        verify(siteRepository).save(any(Site.class));
    }

    @Test
    void existsByName_ShouldReturnTrue_WhenSiteExists() {
        // Given
        when(siteRepository.existsByNameIgnoreCase("Test Site")).thenReturn(true);

        // When
        boolean result = siteService.existsByName("Test Site");

        // Then
        assertThat(result).isTrue();
        verify(siteRepository).existsByNameIgnoreCase("Test Site");
    }

    @Test
    void existsByName_ShouldReturnFalse_WhenSiteDoesNotExist() {
        // Given
        when(siteRepository.existsByNameIgnoreCase("Non-existent Site")).thenReturn(false);

        // When
        boolean result = siteService.existsByName("Non-existent Site");

        // Then
        assertThat(result).isFalse();
        verify(siteRepository).existsByNameIgnoreCase("Non-existent Site");
    }
}
