package com.workeem.workeem_api.tenant.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.workeem.workeem_api.tenant.dto.CreateSiteRequestDto;
import com.workeem.workeem_api.tenant.dto.SiteResponseDto;
import com.workeem.workeem_api.tenant.service.SiteService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(SiteController.class)
class SiteControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SiteService siteService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @WithMockUser(roles = "ADMIN")
    void createSite_ShouldReturnCreatedSite() throws Exception {
        // Given
        CreateSiteRequestDto createRequest = CreateSiteRequestDto.builder()
                .name("Test Site")
                .city("Test City")
                .country("Test Country")
                .address("123 Test Street")
                .isActive(true)
                .spacesCount(10)
                .membersCount(50)
                .build();

        SiteResponseDto expectedResponse = SiteResponseDto.builder()
                .id(1L)
                .name("Test Site")
                .city("Test City")
                .country("Test Country")
                .address("123 Test Street")
                .isActive(true)
                .spacesCount(10)
                .membersCount(50)
                .createdDate(LocalDateTime.now())
                .createdBy("admin")
                .build();

        when(siteService.createSite(any(CreateSiteRequestDto.class))).thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(post("/api/sites")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.name").value("Test Site"))
                .andExpect(jsonPath("$.city").value("Test City"))
                .andExpect(jsonPath("$.country").value("Test Country"))
                .andExpect(jsonPath("$.address").value("123 Test Street"))
                .andExpect(jsonPath("$.isActive").value(true))
                .andExpect(jsonPath("$.spacesCount").value(10))
                .andExpect(jsonPath("$.membersCount").value(50));
    }

    @Test
    @WithMockUser(roles = "USER")
    void getAllSites_ShouldReturnListOfSites() throws Exception {
        // Given
        List<SiteResponseDto> sites = Arrays.asList(
                SiteResponseDto.builder()
                        .id(1L)
                        .name("Site 1")
                        .city("City 1")
                        .country("Country 1")
                        .address("Address 1")
                        .isActive(true)
                        .spacesCount(10)
                        .membersCount(50)
                        .build(),
                SiteResponseDto.builder()
                        .id(2L)
                        .name("Site 2")
                        .city("City 2")
                        .country("Country 2")
                        .address("Address 2")
                        .isActive(true)
                        .spacesCount(15)
                        .membersCount(75)
                        .build()
        );

        when(siteService.getAllSites()).thenReturn(sites);

        // When & Then
        mockMvc.perform(get("/api/sites"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].id").value(1L))
                .andExpect(jsonPath("$[0].name").value("Site 1"))
                .andExpect(jsonPath("$[1].id").value(2L))
                .andExpect(jsonPath("$[1].name").value("Site 2"));
    }

    @Test
    @WithMockUser(roles = "USER")
    void getSiteById_ShouldReturnSite() throws Exception {
        // Given
        Long siteId = 1L;
        SiteResponseDto site = SiteResponseDto.builder()
                .id(siteId)
                .name("Test Site")
                .city("Test City")
                .country("Test Country")
                .address("123 Test Street")
                .isActive(true)
                .spacesCount(10)
                .membersCount(50)
                .build();

        when(siteService.getSiteById(siteId)).thenReturn(site);

        // When & Then
        mockMvc.perform(get("/api/sites/{siteId}", siteId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(siteId))
                .andExpect(jsonPath("$.name").value("Test Site"))
                .andExpect(jsonPath("$.city").value("Test City"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void deleteSite_ShouldReturnNoContent() throws Exception {
        // Given
        Long siteId = 1L;

        // When & Then
        mockMvc.perform(delete("/api/sites/{siteId}", siteId)
                        .with(csrf()))
                .andExpect(status().isNoContent());
    }

    @Test
    void createSite_WithoutAuthentication_ShouldReturnUnauthorized() throws Exception {
        // Given
        CreateSiteRequestDto createRequest = CreateSiteRequestDto.builder()
                .name("Test Site")
                .city("Test City")
                .country("Test Country")
                .address("123 Test Street")
                .build();

        // When & Then
        mockMvc.perform(post("/api/sites")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER")
    void createSite_WithUserRole_ShouldReturnForbidden() throws Exception {
        // Given
        CreateSiteRequestDto createRequest = CreateSiteRequestDto.builder()
                .name("Test Site")
                .city("Test City")
                .country("Test Country")
                .address("123 Test Street")
                .build();

        // When & Then
        mockMvc.perform(post("/api/sites")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isForbidden());
    }
}
